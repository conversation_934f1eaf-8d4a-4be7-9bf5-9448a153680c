{"remainingRequest": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\components\\gongqi\\Electricity4.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\components\\gongqi\\Electricity4.vue", "mtime": 1750745518997}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "name", "props", "gasWarningData", "type", "Array", "default", "data", "mounted", "init", "watch", "handler", "$nextTick", "deep", "methods", "initData", "myChart", "$refs", "echart", "chartData", "length", "value", "xAxisData", "map", "item", "seriesData", "option", "color", "title", "text", "x", "y", "textStyle", "fontSize", "legend", "top", "right", "itemWidth", "itemHeight", "grid", "bottom", "left", "containLabel", "xAxis", "axisLabel", "show", "rotate", "yAxis", "axisTick", "alignWithLabel", "interval", "series", "<PERSON><PERSON><PERSON><PERSON>", "itemStyle", "normal", "graphic", "LinearGradient", "offset", "shadowColor", "<PERSON><PERSON><PERSON><PERSON>", "setOption"], "sources": ["E:\\svn\\***********-0186-SFTianjinUniversityH5\\src\\src\\components\\gongqi\\Electricity4.vue"], "sourcesContent": ["<template>\r\n  <div class=\"echart\" ref=\"echart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\n\r\nexport default {\r\n  name: \"IoTequip\",\r\n  props: {\r\n    gasWarningData: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n\r\n  mounted() {\r\n    this.init();\r\n  },\r\n\r\n  watch: {\r\n    gasWarningData: {\r\n      handler() {\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    initData() { },\r\n    init() {\r\n      const myChart = echarts.init(this.$refs.echart);\r\n\r\n      // 使用传入的数据或默认数据\r\n      const chartData = this.gasWarningData && this.gasWarningData.length > 0\r\n        ? this.gasWarningData\r\n        : [\r\n          { name: \"氧气O₂\", value: 1 },\r\n          { name: \"氢气H₂\", value: 2 },\r\n          { name: \"二氧化碳CO₂\", value: 1 },\r\n          { name: \"一氧化碳CO\", value: 3 },\r\n          { name: \"氨气NH₃\", value: 2 },\r\n          { name: \"甲烷\", value: 1 }\r\n        ];\r\n\r\n      // 提取x轴标签和数据\r\n      const xAxisData = chartData.map(item => item.name);\r\n      const seriesData = chartData.map(item => item.value);\r\n\r\n      const option = {\r\n        color: [\"#3398DB\"],\r\n        title: {\r\n          text: \"个\",\r\n          x: \"6%\",\r\n          y: \"8%\",\r\n          textStyle: {\r\n            color: \"#fff\",\r\n            fontSize: 14,\r\n          },\r\n        },\r\n        legend: {\r\n          data: [\"报警数\"],\r\n          top: \"8%\",\r\n          right: \"40px\",\r\n          itemWidth: 10,\r\n          itemHeight: 10,\r\n          textStyle: {\r\n            color: \"#fff\",\r\n            fontSize: 15\r\n          },\r\n        },\r\n        grid: {\r\n          top: \"18%\",\r\n          bottom: \"6%\",\r\n          left: \"2%\",\r\n          right: \"6%\",\r\n          containLabel: true,\r\n        },\r\n        xAxis: [\r\n          {\r\n            type: \"category\",\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              show: true,\r\n              rotate: 45,  // 设置标签旋转角度为45度\r\n              textStyle: {\r\n                color: \"#fff\", // 将 X 轴标签字体颜色设置为白色\r\n              },\r\n            },\r\n          },\r\n        ],\r\n        yAxis: [\r\n          {\r\n            axisTick: {\r\n              show:false,\r\n              alignWithLabel: false,\r\n            },\r\n            // min:1,\r\n            interval: 1,  // 固定刻度间隔为1\r\n            axisLabel: {\r\n              show: true,\r\n              textStyle: {\r\n                color: \"#fff\",\r\n                fontSize: 15\r\n                // 将 Y 轴标签字体颜色设置为白色\r\n              },\r\n            },\r\n          },\r\n        ],\r\n        series: [\r\n          {\r\n            name: \"报警数\",\r\n            type: \"bar\",\r\n            barWidth: \"40%\",\r\n            data: seriesData,\r\n            itemStyle: {\r\n              normal: {\r\n                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                  {\r\n                    offset: 0,\r\n                    color: \"#66C4FC\",\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: \"#66C4FC\",\r\n                  },\r\n                ]),\r\n                shadowColor: \"rgba(0, 0, 0, 0.1)\",\r\n                shadowBlur: 10,\r\n              },\r\n            },\r\n          },\r\n        ],\r\n      };\r\n\r\n      myChart.setOption(option);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.echart {\r\n  margin-left: 10px;\r\n  width: 95%;\r\n  // margin-top: 20px;\r\n  height: 320px;\r\n}\r\n</style>"], "mappings": ";;AAKA,OAAO,KAAKA,OAAM,MAAO,SAAS;AAElC,eAAe;EACbC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE;IACLC,cAAc,EAAE;MACdC,IAAI,EAAEC,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB;EACF,CAAC;EACDC,IAAIA,CAAA,EAAG;IACL,OAAO,CAAC,CAAC;EACX,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,IAAI,CAAC,CAAC;EACb,CAAC;EAEDC,KAAK,EAAE;IACLP,cAAc,EAAE;MACdQ,OAAOA,CAAA,EAAG;QACR,IAAI,CAACC,SAAS,CAAC,MAAM;UACnB,IAAI,CAACH,IAAI,CAAC,CAAC;QACb,CAAC,CAAC;MACJ,CAAC;MACDI,IAAI,EAAE;IACR;EACF,CAAC;EAEDC,OAAO,EAAE;IACPC,QAAQA,CAAA,EAAG,CAAE,CAAC;IACdN,IAAIA,CAAA,EAAG;MACL,MAAMO,OAAM,GAAIhB,OAAO,CAACS,IAAI,CAAC,IAAI,CAACQ,KAAK,CAACC,MAAM,CAAC;;MAE/C;MACA,MAAMC,SAAQ,GAAI,IAAI,CAAChB,cAAa,IAAK,IAAI,CAACA,cAAc,CAACiB,MAAK,GAAI,IAClE,IAAI,CAACjB,cAAa,GAClB,CACA;QAAEF,IAAI,EAAE,MAAM;QAAEoB,KAAK,EAAE;MAAE,CAAC,EAC1B;QAAEpB,IAAI,EAAE,MAAM;QAAEoB,KAAK,EAAE;MAAE,CAAC,EAC1B;QAAEpB,IAAI,EAAE,SAAS;QAAEoB,KAAK,EAAE;MAAE,CAAC,EAC7B;QAAEpB,IAAI,EAAE,QAAQ;QAAEoB,KAAK,EAAE;MAAE,CAAC,EAC5B;QAAEpB,IAAI,EAAE,OAAO;QAAEoB,KAAK,EAAE;MAAE,CAAC,EAC3B;QAAEpB,IAAI,EAAE,IAAI;QAAEoB,KAAK,EAAE;MAAE,EACxB;;MAEH;MACA,MAAMC,SAAQ,GAAIH,SAAS,CAACI,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACvB,IAAI,CAAC;MAClD,MAAMwB,UAAS,GAAIN,SAAS,CAACI,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACH,KAAK,CAAC;MAEpD,MAAMK,MAAK,GAAI;QACbC,KAAK,EAAE,CAAC,SAAS,CAAC;QAClBC,KAAK,EAAE;UACLC,IAAI,EAAE,GAAG;UACTC,CAAC,EAAE,IAAI;UACPC,CAAC,EAAE,IAAI;UACPC,SAAS,EAAE;YACTL,KAAK,EAAE,MAAM;YACbM,QAAQ,EAAE;UACZ;QACF,CAAC;QACDC,MAAM,EAAE;UACN3B,IAAI,EAAE,CAAC,KAAK,CAAC;UACb4B,GAAG,EAAE,IAAI;UACTC,KAAK,EAAE,MAAM;UACbC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,EAAE;UACdN,SAAS,EAAE;YACTL,KAAK,EAAE,MAAM;YACbM,QAAQ,EAAE;UACZ;QACF,CAAC;QACDM,IAAI,EAAE;UACJJ,GAAG,EAAE,KAAK;UACVK,MAAM,EAAE,IAAI;UACZC,IAAI,EAAE,IAAI;UACVL,KAAK,EAAE,IAAI;UACXM,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE,CACL;UACEvC,IAAI,EAAE,UAAU;UAChBG,IAAI,EAAEe,SAAS;UACfsB,SAAS,EAAE;YACTC,IAAI,EAAE,IAAI;YACVC,MAAM,EAAE,EAAE;YAAG;YACbd,SAAS,EAAE;cACTL,KAAK,EAAE,MAAM,CAAE;YACjB;UACF;QACF,CAAC,CACF;QACDoB,KAAK,EAAE,CACL;UACEC,QAAQ,EAAE;YACRH,IAAI,EAAC,KAAK;YACVI,cAAc,EAAE;UAClB,CAAC;UACD;UACAC,QAAQ,EAAE,CAAC;UAAG;UACdN,SAAS,EAAE;YACTC,IAAI,EAAE,IAAI;YACVb,SAAS,EAAE;cACTL,KAAK,EAAE,MAAM;cACbM,QAAQ,EAAE;cACV;YACF;UACF;QACF,CAAC,CACF;QACDkB,MAAM,EAAE,CACN;UACElD,IAAI,EAAE,KAAK;UACXG,IAAI,EAAE,KAAK;UACXgD,QAAQ,EAAE,KAAK;UACf7C,IAAI,EAAEkB,UAAU;UAChB4B,SAAS,EAAE;YACTC,MAAM,EAAE;cACN3B,KAAK,EAAE,IAAI3B,OAAO,CAACuD,OAAO,CAACC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CACpD;gBACEC,MAAM,EAAE,CAAC;gBACT9B,KAAK,EAAE;cACT,CAAC,EACD;gBACE8B,MAAM,EAAE,CAAC;gBACT9B,KAAK,EAAE;cACT,CAAC,CACF,CAAC;cACF+B,WAAW,EAAE,oBAAoB;cACjCC,UAAU,EAAE;YACd;UACF;QACF,CAAC;MAEL,CAAC;MAED3C,OAAO,CAAC4C,SAAS,CAAClC,MAAM,CAAC;IAC3B;EACF;AACF,CAAC", "ignoreList": []}]}