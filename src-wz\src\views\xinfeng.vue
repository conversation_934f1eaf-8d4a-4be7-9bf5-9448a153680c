<template>
  <div>
    <!-- <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component> -->
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="isshowwhat"></component>
    <div class="botbtn">
      <div v-for="(item, index) in changeTitle" :key="index" :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)">
        {{ item }}
      </div>
    </div>
    <!-- <tedai
      :ids="ids"
      :selectedItem="selectedItem"
      class="sbdetails"
      :zengtiimg="zengtiimg"
      v-if="isshow"
      @hidedetails="hidedetails"
    ></tedai> -->
    <tedai :ids="ids" :selectedItem="selectedItem" :tfdata="tfdata" :tfchart="tfchart" class="sbdetails"
      :zengtiimg="zengtiimg" v-if="isshowsss" @hidedetails="hidedetailsss"></tedai>
    <biaoGe :Title="Title" @xuanze-dialog="xuanzedialog" v-if="isshow" @hidedetails="hidedetails"
      :tableTitle="tableTitle" :tableDataItem="tableDataItem"></biaoGe>
    <div class="container" v-if="!isshowwhat">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title2 @open-dialog="opendialog" class="ltitle1" tit="新排风监控">
          <div class="box">
            <div>
              <el-input class="el-input" v-model="input" placeholder="请输入内容"></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->

            <div class="menu">
              <div v-for="(menu, index) in menus" :key="index" class="menu-group">
                <div :style="{
                  color: activeSubmenu == menu.id ? '#00ffc0' : '',
                }" class="menu-item" @click="toggleSubMenu(menu.id, index)">
                  {{ menu.title }}
                </div>
                <div v-show="activeSubmenu === menu.id" class="submenu">
                  <div v-for="(item, subIndex) in menu.submenu" :style="{
                    color: activeSubSubmenu === item.id ? '#00ffc0' : '',
                  }" :key="subIndex" class="submenu-items">
                    <div class="bq" @click="toggleSubSubMenu(item.id, index)">
                      {{ item.title }}
                    </div>
                    <div v-show="activeSubSubmenu === item.id" class="submenu">
                      <div v-for="(subItem, thirdIndex) in item.submenu" :key="thirdIndex" :style="{
                        color: selectedIndex == thirdIndex ? '#00ffc0' : '',
                      }" class="submenu-item" @click="setContent(subItem, thirdIndex)">
                        {{ subItem.title }}
                        <!-- <div class="listtype">使用中</div> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <!-- <iframe id="ifram" class="zutai" src="scidh3dview-tj/index.html" frameborder="0"></iframe> -->

      <div class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title3 tit="新风监控详情" v-if="isshowsss">
          <div class="xinf">
            <div class="info-panel">
              <h3 class="title">铭牌参数</h3>
              <div v-for="(item, index) in istype == 1
                ? deviceInfoList
                : deviceInfoList1" :key="index" class="info-row single-line">
                <div class="info-item">
                  <span class="label">{{ item.label }}</span>
                  <span class="value">{{ item.value }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="xfin">
            <div class="system-rules">
              <h3 class="title">管理制度</h3>
              <div class="content">
                <!-- <p class="paragraph">
                  为了加强通风系统的管理，确保井下空气质量，防止由于通风造成的安全隐患，保障员工健康和安全生产的顺序进行，根据《金属非金属矿山安全规程》GB16423—2006，结合公司的实际情况，特制定本制度。
                </p> -->
                <p class="paragraph">
                  {{ istype == 1 ? managementRules : managementRules1 }}
                </p>
              </div>
            </div>
          </div>
        </Title3>
      </div>
    </div>
  </div>
</template>

<script>
import { getDeviceData, getDevicedetails } from "@/api/device";
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";

import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedais_xf.vue";
// import tedai from "@/components/common/hj-tedais.vue";
import biaoGe from "@/components/common/biaoGe.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import component0 from "@/views/tongji/xinfeng.vue";
// import details from "@/components/common/details.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    tedai,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGe,
    component0,
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      tfdata: [],
      tfchart: [],
      isshowwhat: true,
      actindex: 0,
      titactive: 0,
      changeTitle: ["数据统计", "数据列表"],
      Title: "新风监控",
      isshowsss: false,
      activeSubmenu: null, // 当前激活的二级菜单
      activeyj: null, // 当前激活的一级菜单
      activeSubSubmenu: null, // 当前激活的三级菜单
      activeContent: null, // 当前显示的内容
      selectedIndex: null,
      selectedItem: null,
      menus: [
        {
          id: "全部系统",
          title: "全部系统",
          submenu: [],
        },
        {
          id: "zutai1",
          title: "新风系统",
          submenu: [
            {
              id: "5F",
              title: "5F",
              submenu: [
                {
                  title: "XF-501",
                  content: "d3ktx_wk021",
                  deviceId: "1121013",
                },
                {
                  title: "XF-502",
                  content: "d3ktx_wk022",
                  deviceId: "1121014",
                },
                {
                  title: "XF-503",
                  content: "d3ktx_wk023",
                  deviceId: "1121015",
                },
                {
                  title: "XF-504",
                  content: "d3ktx_wk024",
                  deviceId: "1121016",
                },

                {
                  title: "K-501",
                  content: "d3ktx_wk026",
                  deviceId: "1121017",
                },
                // {
                //   title: "d3ktx_wk027",
                //   content: "d3ktx_wk027",
                // },
                // {
                //   title: "d3ktx_wk028",
                //   content: "d3ktx_wk028",
                // },
              ],
            },
            {
              id: "4F",
              title: "4F",
              submenu: [
                {
                  title: "XF-401",
                  content: "d3ktx_wk013",
                  deviceId: "1121010",
                },
                {
                  title: "XF-402",
                  content: "d3ktx_wk014",
                  deviceId: "1121011",
                },
                {
                  title: "XF-403",
                  content: "d3ktx_wk015",
                  deviceId: "1121012",
                },
                // {
                //   title: "d3ktx_wk016",
                //   content: "d3ktx_wk016",
                // },
                // {
                //   title: "d3ktx_wk017",
                //   content: "d3ktx_wk017",
                // },
                // {
                //   title: "d3ktx_wk018",
                //   content: "d3ktx_wk018",
                // },
                // {
                //   title: "d3ktx_wk019",
                //   content: "d3ktx_wk019",
                // },
                // {
                //   title: "d3ktx_wk020",
                //   content: "d3ktx_wk020",
                // },
              ],
            },
            {
              id: "3F",
              title: "3F",
              submenu: [
                {
                  title: "XF-301",
                  content: "d3ktx_wk009",
                  deviceId: "1121009",
                },
               
              ],
            },
            {
              id: "2F",
              title: "2F",
              submenu: [
                {
                  title: "XF-201",
                  content: "d3ktx_wk001",
                  deviceId: "1121008",
                },
               
              ],
            },
            {
              id: "B1F",
              title: "B1F",
              submenu: [
              {
                  title: "XFD101",
                  content: "d3ktx_wk002",
                  deviceId: "1121001",
                },
                {
                  title: "XFD102",
                  content: "d3ktx_wk003",
                  deviceId: "1121002",
                },
                {
                  title: "XFD103",
                  content: "d3ktx_wk004",
                  deviceId: "1121003",
                },
                {
                  title: "XFD104",
                  content: "d3ktx_wk005",
                  deviceId: "1121004",
                },
                {
                  title: "XFD105",
                  content: "d3ktx_wk006",
                  deviceId: "1121005",
                },
                {
                  title: "XFD106",
                  content: "d3ktx_wk007",
                  deviceId: "1121006",
                },
                {
                  title: "XFD107",
                  content: "d3ktx_wk008",
                  deviceId: "1121007",
                },
              ],
            },
          ],
        },
        {
          id: "zutai2",
          title: "排风系统",
          submenu: [
            {
              id: "P-WD01(M)",
              title: "整体",
              submenu: [
                {
                  id: "P-WD01(M)",
                  title: "P-WD01(M)",
                  submenu: [],
                },
                {
                  id: "P-WD04(M)",
                  title: "P-WD04(M)",
                  submenu: [],
                },
                {
                  id: "P-WD04(N)",
                  title: "P-WD04(N)",
                  submenu: [],
                },
                {
                  id: "P-WD05(S)",
                  title: "P-WD05(S)",
                  submenu: [],
                },
                {
                  id: "P-WD06(N)",
                  title: "P-WD06(N)",
                  submenu: [],
                },
                {
                  id: "P-WD07(N)",
                  title: "P-WD07(N)",
                  submenu: [],
                },
              ],
            },
          ],
        },
        {
          id: "zutai3",
          title: "送风系统",
          submenu: [
            {
              id: "5F",
              title: "5F",
              submenu: [
                {
                  title: "d3ktx_wk021",
                  content: "d3ktx_wk021",
                },
                {
                  title: "d3ktx_wk022",
                  content: "d3ktx_wk022",
                },
                {
                  title: "d3ktx_wk023",
                  content: "d3ktx_wk023",
                },
                {
                  title: "d3ktx_wk024",
                  content: "d3ktx_wk024",
                },
                {
                  title: "d3ktx_wk025",
                  content: "d3ktx_wk025",
                },
                {
                  title: "d3ktx_wk026",
                  content: "d3ktx_wk026",
                },
                {
                  title: "d3ktx_wk027",
                  content: "d3ktx_wk027",
                },
                {
                  title: "d3ktx_wk028",
                  content: "d3ktx_wk028",
                },
              ],
            },
            {
              id: "4F",
              title: "4F",
              submenu: [
                {
                  title: "d3ktx_wk013",
                  content: "d3ktx_wk013",
                },
                {
                  title: "d3ktx_wk014",
                  content: "d3ktx_wk014",
                },
                {
                  title: "d3ktx_wk015",
                  content: "d3ktx_wk015",
                },
                {
                  title: "d3ktx_wk016",
                  content: "d3ktx_wk016",
                },
                {
                  title: "d3ktx_wk017",
                  content: "d3ktx_wk017",
                },
                {
                  title: "d3ktx_wk018",
                  content: "d3ktx_wk018",
                },
                {
                  title: "d3ktx_wk019",
                  content: "d3ktx_wk019",
                },
                {
                  title: "d3ktx_wk020",
                  content: "d3ktx_wk020",
                },
              ],
            },
            {
              id: "3F",
              title: "3F",
              submenu: [
                {
                  title: "d3ktx_wk009",
                  content: "d3ktx_wk009",
                },
                {
                  title: "d3ktx_wk010",
                  content: "d3ktx_wk010",
                },
                {
                  title: "d3ktx_wk011",
                  content: "d3ktx_wk011",
                },
                {
                  title: "d3ktx_wk012",
                  content: "d3ktx_wk012",
                },
              ],
            },
            {
              id: "2F",
              title: "2F",
              submenu: [
                {
                  title: "d3ktx_wk001",
                  content: "d3ktx_wk001",
                },
                {
                  title: "d3ktx_wk002",
                  content: "d3ktx_wk002",
                },
                {
                  title: "d3ktx_wk003",
                  content: "d3ktx_wk003",
                },
                {
                  title: "d3ktx_wk008",
                  content: "d3ktx_wk008",
                },
              ],
            },
            {
              id: "B1F",
              title: "B1F",
              submenu: [
                {
                  title: "d3ktx_wk004",
                  content: "d3ktx_wk004",
                },
                {
                  title: "d3ktx_wk005",
                  content: "d3ktx_wk005",
                },
                {
                  title: "d3ktx_wk006",
                  content: "d3ktx_wk006",
                },
                {
                  title: "d3ktx_wk007",
                  content: "d3ktx_wk007",
                },
              ],
            },
          ],
        },
      ],
      activeSubmenu: null, // 当前激活的子菜单
      activeContent: null, // 当前显示的内容
      newArr: [],
      isshow: false,
      xxxx: false,
      cgqlist: [],
      listtable: [],
      data: [
        {
          id: "zutai",
          category: "全部系统",
          items: [],
        },
        {
          id: "zutai1",
          category: "新风系统",
          items: [
            {
              name: "XF-D101",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
          ],
        },
        {
          id: "zutai2",
          category: "排风系统",
          items: [
            {
              id: "P-WD01(M)",
              name: "P-WD01(M)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              id: "P-WD01(N)",
              name: "P-WD01(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD01(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD010(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD010(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD02(M)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD02(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD02(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD03(M)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD03(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD03(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD04(M)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD04(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD04(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD05(M)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD05(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD05(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD06(M)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD06(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD06(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD07(M)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD07(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD07(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD08(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD08(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD09(N)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
            {
              name: "P-WD09(S)",
              number: "暂未入账（2207000840）",
              room: "A1003",
              sbtypess: "预约中",
              loucheng: "1层",
              type: "预约中",
            },
          ],
        },
        {
          id: "zutai3",
          category: "送风系统",
          items: [],
        },
      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      selectedItem: null,
      tableDataItem: [],

      tableTitle: [
        { key: "楼层" },
        { key: "设备编号" },
        { key: "设备名称" },
        { key: "房间号" },
        { key: "模型" },
        { key: "设备状态" },
        { key: "状态说明" },
      ],
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",

      deviceInfoList: [
        { label: "机组型号:", value: "MDM0508-E5" },
        { label: "电源:", value: "380V/3N~/50Hz" },
        { label: "机组编号:", value: "XF-D101" },
        { label: "出水方向:", value: "L" },
        { label: "额定风量:", value: "3600m³/h" },
        { label: "机外全压/机外静压:", value: "500Pa/413Pa" },
        { label: "风机转速:", value: "4192RPM" },
        { label: "机组全压:", value: "863Pa" },
        { label: "电机功率/输入功率:", value: "2.20kW/2.71kW" },
        { label: "供冷量:", value: "37.3kW" },
        { label: "供热量:", value: "0.00kW" },
        { label: "执行标准:", value: "GB/T 14294-2008" },
      ],
      managementRules:
        "新风空调系统由专人负责，建立设备档案，定期检查维护，严禁擅自拆改。按需运行，确保空气流通与节能。定期检测空气质量（PM2.5、CO₂、温湿度），清洁过滤网及相关部件，保障系统高效稳定。发现异常须及时报修，确保正常使用。",

      deviceInfoList1: [
        { label: "机号", value: "NO 4" },
        { label: "型号", value: "SFG4-2R" },
        { label: "风量", value: "11000 m³/h" },
        { label: "全压", value: "270 Pa" },
        { label: "功率", value: "1.5 kW" },
        { label: "电压", value: "220V" },
        { label: "转速", value: "2800 r/min" },
        { label: "频率", value: "50 Hz" },
        { label: "出厂日期", value: "20XX 年 X 月" },
      ],
      managementRules1:
        "为保证实验室的排风效果和维持室内温、湿度要求，应设置实验室最小换气次数。白天有人操作时8-10次/h，夜间没有人操作时为4-6次/h.如果实验室各通风柜及万向罩和其他排风设备的最小排风量的总和仍不能满足实验室规定的最小通风换气次数要求，这时应在实验室内增设房间排风口，增加实验室排风量，以保证实验室所要求的最低通风换气量。",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    changetit(index) {
      this.titactive = index;
      this.isshowwhat = !index;
      if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
      } else {
        this.showdh = true;
        this.showdh1 = false;
        this.noAnimation = false;
      }
    },
    toggleSubSubMenu(submenuId, index) {
      this.activeSubSubmenu =
        this.activeSubSubmenu === submenuId ? null : submenuId;
      this.selectedIndex = null;
      this.sbtitle1 = index;
    },
    hidedetailsss() {
      this.isshowsss = false;
    },
    opendialog(payload) {
      if (payload == 1) {
        this.isshow = true;

        this.data.forEach((item) => {
          if (item.category == "电子显微镜") {
            this.isshow = true;
            this.tableDataItem = item.items;
            console.log(this.tableDataItem);
          }
        });
      }

      // 在这里处理事件
    },
    toggleSubMenu(menuId, index) {
      this.actindex = index;
      this.activeSubmenu = this.activeSubmenu === menuId ? null : menuId;
      console.log(menuId, "menuId");
      this.seed(menuId);
      if (index == 2) {
        this.istype = 0;
      } else {
        this.istype = 1;
      }
      this.isshowsss = false;
    },
    seed(item) {
      const frame = document.getElementById("ifram");
      console.log(item, frame);
      frame.contentWindow.postMessage(
        {
          type: "zutai",
          param: { data: item }, //item为对应的系统名称 或者缩写
        },
        "*"
      );
    },
    processData(dataArray) {
      return dataArray.map(item => {
        // 如果 dOtherData 为空，则跳过处理
        if (!item.dOtherData) {
          return item;
        }

        // 解析 dOtherData 的映射关系（格式如 "0:正常;1:故障;"）
        const mapping = {};
        const pairs = item.dOtherData.split(';');

        pairs.forEach(pair => {
          if (pair) {
            const [key, value] = pair.split(':');
            if (key && value) {
              mapping[key.trim()] = value.trim();
            }
          }
        });

        // 如果 dVal 在映射表中存在，则设置 drVal 为对应的描述
        if (mapping[item.dVal] !== undefined) {
          return {
            ...item,
            dVal: mapping[item.dVal]
          };
        }

        // 否则返回原对象（drVal 可能为空或保持不变）
        return item;
      });
    },
    // 设置内容
    async setContent(content) {
      // this.isshowsss = true;
      this.selectedItem = content.title;
      console.log(content, "content");
      this.activeContent = content;

      // 获取设备数据并更新图表
      try {
        const res = await getDeviceData(content.deviceId, content.deviceId + '7');
        const res1 = await getDevicedetails(content.deviceId);
        let Temperature = res1.data.deviceDataBase.find(item => item.dmName == "温度")?.dmValue || '15';
        let Humidity = res1.data.deviceDataBase.find(item => item.dmName == "湿度")?.dmValue || '20';
        console.log(Temperature, Humidity, "Temperature,Humidity");
        const chartData = res.data.data || [];
        const times = chartData.map((item) => item.recordedAt.slice(11, 16));
        const values = chartData.map((item) => item.indication);
        this.tfdata = this.processData(res1.data.deviceDataBase);
        console.log(this.tfdata, "this.tfdata");
        this.tfchart = {
          xAxis: {
            data: times,
          },
          series: [{ data: values }],
        };
        if (this.actindex == 2) {
          this.$emit("seedxfj", content.content, content.title, Temperature, Humidity);
        } else if (this.actindex == 0) {

        } else {
          this.$emit("seedxfj", content.content, content.title, Temperature, Humidity);
        }
        console.log(res1.data.deviceDataBase, "res11");

      } catch (error) {
        console.error("获取设备数据失败:", error);
      }
    },
    showdetails(item) {
      // item.items.forEach((item) => {
      //   this.newArr.push({ name: item.name });
      // });
      // console.log(this.newArr);
      this.isshow = true;
      this.tableDataItem = item.items;
    },
    hidedetails() {
      this.isshow = false;
    },

    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
    xuanzedialog(value) {
      const optionMapping = {
        选项1: 0,
        选项2: 1,
        选项3: 2,
        选项4: 3,
        选项5: 4,
        选项6: 5,
        选项7: 6,
        选项8: 7,
        选项9: 8,
        选项10: 9,
        选项11: 10,
        选项12: 11,
        选项13: 12,
      };

      const index = optionMapping[value];
      if (index !== undefined) {
        this.tableDataItem = this.data[index].items;
      } else {
        console.error("无效的选项: ", value);
      }
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    // setTimeout(() => {
    //   this.showdh1 = false;
    //   this.noAnimation = false;
    // }, 1000); // 动画持续时间为1秒
    var that = this;
    window.addEventListener("message", (event) => {
      console.log(event, "收到了");
      if (event.data.type == "xfg") {
        that.isshowsss = true;
        // that.selectedItem = event.data.data;
      }
    });
    console.log(1222);
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.botbtn {
  position: fixed;
  top: 978px;
  right:890px;
  // right: 703px;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;
  z-index: 20;

  .zutai {
    position: fixed;
    left: 411px;
    top: 118px;
    height: 860px;
    width: 1095px;
    z-index: 20;
  }

  .sbdetails {
    position: absolute;
    top: 45%;
    left: 48.15%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    width: 50%;
    height: 70%;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    height: 800px;
    width: 330px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 305px;
      height: 34px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 10px;
        color: #ffffff;
        display: flex;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}

.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}

/* 菜单容器 */
.menu-container {
  display: flex;
}

/* 菜单样式 */
.menu {
  width: 100%;
  // background-color: #fff;
  // border: 1px solid #ddd;
  border-radius: 4px;
}

/* 菜单项样式 */
.menu-group {
  margin-top: 14px;
}

.menu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 100%;
  height: 36px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding-left: 10px;
  // justify-content: center;
}

.menu-item1 {
  color: #00ffc0 !important;
  // justify-content: center;
}

.menu-item:hover {
  background-color: #f0f0f0 !important;
  color: #00ffc0;
}

.submenu {
  display: block;
  /* 确保子菜单是块级元素 */
  // background-color: #f9f9f9;
  padding-left: 8px;
}

.submenu-items:hover {
  color: #00ffc0;
}

.bq {
  text-align: left;
  width: 108px;
}

.submenu-item:hover {
  color: #00ffc0;
}

.submenu-items {
  cursor: pointer;
  // background-color:#013363;
  width: 100%;

  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  flex-direction: column;
  /* 垂直方向排列 */
  align-items: flex-start;
  padding-left: 10px;
  margin-top: 10px;
}

.submenu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 300px !important;
  /* 移除固定高度 */
  /* height: 30px; */
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 15px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-left: 10px;

  margin-top: 10px;
}

.qiuqiu {
  display: flex;
  align-items: center;

  .siqiu {
    width: 16px;
    height: 16px;
    margin-left: 10px;
    margin-right: 7px;
  }
}

.listtype {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 80px;
  height: 26px;
  text-align: center;
  line-height: 26px;

  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 190px;
  /* 你可以根据需要调整宽度 */
  font-size: 15px;
}

.xinf {
  // padding: 15px;

  .title {
    color: #4badff;
    font-size: 16px;
    margin-bottom: 6px;
  }

  .info-panel {
    background: rgba(0, 20, 40, 0.3);
    border-radius: 8px;
    padding: 15px;
  }

  .info-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    &.single-line {
      border-bottom: 1px solid rgba(75, 173, 255, 0.1);
      padding: 3px 0;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .info-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      color: #ffffff;
      margin-right: 8px;
      font-size: 14px;
      white-space: nowrap;
    }

    .value {
      color: #4badff;
      font-size: 14px;
    }
  }
}

.xfin {
  margin-top: 20px;

  .system-rules {
    background: rgba(0, 20, 40, 0.3);
    border-radius: 8px;
    padding: 20px;

    .title {
      color: #4badff;
      font-size: 16px;
      margin-bottom: 16px;
    }

    .content {
      .paragraph {
        color: #ffffff;
        font-size: 14px;
        line-height: 1.8;
        margin-bottom: 12px;
        text-indent: 2em;
        text-align: justify;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
