var comdata;
var buildnum;
window.addEventListener("message", function (event) {
  //event.data获取传过来的数据

  if (event.data.type == "function") {
    let name = event.data.name;
    let comdata = event.data.param;
    console.log(comdata, "收到了");
    if (comdata) {
      // console.log(labelNamesArray,11222);

      // view.removeObjByNames(labelNamesArray);
      xzrecord = comdata.data;
      // view.setLayer(["灯光"]);
      if (comdata.type == 1) {
        // view.resetLayer();
        if (
          comdata.data == "复位" ||
          comdata.data == "首页" ||
          comdata.data == "整体场景"
        ) {
          // resetLayer();
          // view.resetCamera();
          view.setLayer(["JZ1_Building_01", "JZ2_Building_01", "dm",'dm_dx']);

          view.animateCamera(
            {
              x: 48.12188990651303,
              y: 62.89422173478372,
              z: 30.265675265868214,
            },
            {
              x: -44.10985242127187,
              y: 1.4901579019901197,
              z: 30.71627393509893,
            },
            0
          );
        } else if (comdata.data == "标签") {
          addlable1(
            view.getObjCenterByNames(view.searchAllByName("智能化设备网桥架")),
            comdata.value
          );
        }
      }
    }
  } else if (event.data.type == "build") {
    let comdata = event.data.param;

    console.log(comdata);
    if (comdata.data && comdata.data != "整体建筑") {
      function getObjectByOuterTitle(title) {
        // 查找匹配的对象
        const outerObject = floorlist.find((item) => item.title === title);

        if (!outerObject) {
          return { item: null, index: -1 }; // 未找到匹配项时返回
        }

        const index = floorlist.indexOf(outerObject); // 获取匹配对象的索引

        return { item: outerObject, index };
      }
      buildnum = getObjectByOuterTitle(comdata.data).index;
      console.log(floorlist[buildnum], buildnum, 39);
      rightBtn.labelTitleFun(floorlist[buildnum], buildnum);
    } else {
      addImgtab([]);
      view.setLayer(["JZ1_Building_01", "JZ2_Building_01", "dm",'dm_dx']);
      view.animateCamera(
        {
          x: 48.12188990651303,
          y: 62.89422173478372,
          z: 30.265675265868214,
        },
        {
          x: -44.10985242127187,
          y: 1.4901579019901197,
          z: 30.71627393509893,
        },
        1000
      );
      showCarFlow2();
    }
  } else if (event.data.type == "floor") {
    let comdata = event.data.param;

    console.log(comdata, "comdata");
    let data = floorlist[buildnum].floor;
    console.log(floorlist);
    if (comdata.data) {
      console.log(data, 52);
      if (comdata.data == "整体") {
      } else {
        function getObjectsAndIndexesByTitle(title) {
          return data
            .map((item, index) => ({ item, index })) // 将对象和索引一起返回
            .filter(({ item }) => item.title === title); // 筛选匹配的对象
        }
        const result = getObjectsAndIndexesByTitle(comdata.data);

        rightBtn.rightBtnFun(
          result[0].item.name,
          result[0].item.pos,
          result[0].item.tar,
          result[0].item.title,
          result[0].index,
          "",
          comdata.devicename,
          comdata.flag
        );
        console.log(json, "json");
        setTimeout(() => {
          addldbq1(json.find((item) => item.floor == comdata.data).roomlist);
        }, 100);
        // showAllLabels();
        // setTimeout(() => {
        //   hideAllLabels();
        //   setTimeout(() => {
        //     showAllLabels();
        //   }, 3000);
        // }, 4000);
      }
    } else {
      rightBtn.labelTitleFun(floorlist[buildnum], buildnum);
    }
  } else if (event.data.type == "sbid") {
    let comdata = event.data.param.data;
    console.log(comdata, "sbid");

    // console.log(view.getObjViewInfoByModelId("500746"));
    jujiao(Number(comdata));
    selectLabelById(comdata);
    // const checkInfo = setInterval(() => {
    //   const info = view.getObjViewInfoByModelId(Number(comdata));

    //   if (info) {
    //     clearInterval(checkInfo); // 停止轮询
    //     const { position, target } = info;
    //     position.y += 62;
    //     console.log(position, 12589);
    //     view.animateCamera(position, target, 500);
    //     selectLabelById(comdata);
    //   }
    //   // changeLabelColorById(Number(comdata), "枪式摄像头");
    // }, 100);
    // const info = view.getObjViewInfoByModelId(Number(comdata));
    // const { position, target } = info;
    // view.animateCamera(position, target, 500);
  } else if (event.data.type == "feiru") {
    let comdata = event.data.param;
    console.log(comdata, "feiru");

    view.animateCamera(
      JSON.parse(comdata.position),
      JSON.parse(comdata.target),
      500
    );

    // const info = view.getObjViewInfoByModelId(comdata);
    // const { position, target } = info;
    // view.animateCamera(position, target, 500);
  } else if (event.data.type == "touming") {
    let comdata = event.data.param;
    console.log(comdata, "touming");
    //实验楼透明
    let foor = {
      title: "实验楼",
      name: ["JZ1_Building_01", "JZ2_Building_01", "dm",'dm_dx'],
      pos: {
        x: 48.12188990651303,
        y: 62.89422173478372,
        z: 30.265675265868214,
      },
      tar: {
        x: -44.10985242127187,
        y: 1.4901579019901197,
        z: 30.71627393509893,
      },
      floor: [
        {
          title: "楼顶",
          pos: {
            x: 9.223440558558195,
            y: 159.5516673445813,
            z: 8.399120573928856,
          },
          tar: {
            x: -43.35588586108212,
            y: -7.532907045957097,
            z: 8.893870862878108,
          },
          name: ["JZ1_Building_01", "JZ2_Building_01", "dm",'dm_dx'],
        },
        {
          title: "5F",
          pos: {
            x: 9.223440558558195,
            y: 159.5516673445813,
            z: 8.399120573928856,
          },
          tar: {
            x: -43.35588586108212,
            y: -7.532907045957097,
            z: 8.893870862878108,
          },
          name: [
            "dm",
            "JZ1_F2_SN_Model_Building_01",

            "JZ2_F2_SN_Model_Building_01",
            "JZ1_F1_SN_Model_Building_01",

            "JZ2_F1_SN_Model_Building_01",
            "JZ1_B1_SN_Model_Building_01",
            "JZ1_F3_SN_Model_Building_01",
            "JZ2_F3_SN_Model_Building_01",'dm_dx',
            "JZ1_F4_SN_Model_Building_01",
            "JZ2_F4_SN_Model_Building_01",
            "JZ1_F5_SN_Model_Building_01",
            "JZ2_F5_SN_Model_Building_01",
          ],
        },
        {
          title: "4F",
          pos: {
            x: 9.223440558558195,
            y: 159.5516673445813,
            z: 8.399120573928856,
          },
          tar: {
            x: -43.35588586108212,
            y: -7.532907045957097,
            z: 8.893870862878108,
          },
          name: [
            "dm",'dm_dx',
            "JZ1_F2_SN_Model_Building_01",

            "JZ2_F2_SN_Model_Building_01",
            "JZ1_F1_SN_Model_Building_01",

            "JZ2_F1_SN_Model_Building_01",
            "JZ1_B1_SN_Model_Building_01",
            "JZ1_F3_SN_Model_Building_01",
            "JZ2_F3_SN_Model_Building_01",
            "JZ1_F4_SN_Model_Building_01",
            "JZ2_F4_SN_Model_Building_01",
          ],
        },
        {
          title: "3F",
          pos: {
            x: 9.223440558558195,
            y: 159.5516673445813,
            z: 8.399120573928856,
          },
          tar: {
            x: -43.35588586108212,
            y: -7.532907045957097,
            z: 8.893870862878108,
          },
          name: [
            "dm",'dm_dx',
            "JZ1_F2_SN_Model_Building_01",

            "JZ2_F2_SN_Model_Building_01",
            "JZ1_F1_SN_Model_Building_01",

            "JZ2_F1_SN_Model_Building_01",
            "JZ1_B1_SN_Model_Building_01",
            "JZ1_F3_SN_Model_Building_01",
            "JZ2_F3_SN_Model_Building_01",
          ],
        },
        {
          title: "2F",
          pos: {
            x: 9.223440558558195,
            y: 159.5516673445813,
            z: 8.399120573928856,
          },
          tar: {
            x: -43.35588586108212,
            y: -7.532907045957097,
            z: 8.893870862878108,
          },
          name: [
            "dm",'dm_dx',
            "JZ1_F2_SN_Model_Building_01",

            "JZ2_F2_SN_Model_Building_01",
            "JZ1_F1_SN_Model_Building_01",

            "JZ2_F1_SN_Model_Building_01",
            "JZ1_B1_SN_Model_Building_01",
          ],
        },
        {
          title: "1F",
          pos: {
            x: 9.223440558558195,
            y: 159.5516673445813,
            z: 8.399120573928856,
          },
          tar: {
            x: -43.35588586108212,
            y: -7.532907045957097,
            z: 8.893870862878108,
          },
          name: [
            "JZ1_F1_SN_Model_Building_01",

            "JZ2_F1_SN_Model_Building_01",
            "JZ1_B1_SN_Model_Building_01",
            "dm",'dm_dx',
          ],
        },
        {
          title: "B1F",
          pos: {
            x: -9.898794069500234,
            y: 79.58884772637656,
            z: 30.146650335501832,
          },
          tar: {
            x: -42.75308851051134,
            y: -9.75274042843543,
            z: 30.60458207854196,
          },
          name: ["dm", "JZ1_B1_SN_Model_Building_01"],
        },
      ],
    };
    if (comdata.data == "新风监控") {
      // view.setLayer(["floor_L"]);

      rightBtn.labelTitleFun(foor, 0);
      view.setOpa(
        ["f1_L", "f2_L", "f3_L", "f4_L", "f5_L", "Rectangle179", "Line025"],
        0.15
      );

      // setTimeout(() => {
      //   view.resetOpaNames(["f5_L"],0.25);
      // }, 500);
      // let comdata = event.data.param;
      // console.log(comdata, "feiru");
    } else {
      rightBtn.labelTitleFun(foor, 0);
    }
    // view.animateCamera(JSON.parse(comdata.position), JSON.parse(comdata.target), 500);

    // const info = view.getObjViewInfoByModelId(comdata);
    // const { position, target } = info;
    // view.animateCamera(position, target, 500);
  } else if (event.data.type == "bq") {
    let comdata = event.data.param;
    if (comdata.data) {
      showAllLabels();
    } else {
      hideAllLabels();
    }
    // setTimeout(() => {
    //   hideAllLabels();
    //   setTimeout(() => {
    //     showAllLabels();
    //   }, 3000);
    // }, 4000);
  }
});
