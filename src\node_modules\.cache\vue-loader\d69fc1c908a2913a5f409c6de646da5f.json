{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\components\\gongqi\\Electricity4.vue?vue&type=template&id=13ae6d79&scoped=true", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\components\\gongqi\\Electricity4.vue", "mtime": 1750745518997}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9ImVjaGFydCIgcmVmPSJlY2hhcnQiPjwvZGl2Pg0K"}, {"version": 3, "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\components\\gongqi\\Electricity4.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "E:/svn/SH-20240918-0186-SFTianjinUniversityH5/src/src/components/gongqi/Electricity4.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"echart\" ref=\"echart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\n\r\nexport default {\r\n  name: \"IoTequip\",\r\n  props: {\r\n    gasWarningData: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n\r\n  mounted() {\r\n    this.init();\r\n  },\r\n\r\n  watch: {\r\n    gasWarningData: {\r\n      handler() {\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    initData() { },\r\n    init() {\r\n      const myChart = echarts.init(this.$refs.echart);\r\n\r\n      // 使用传入的数据或默认数据\r\n      const chartData = this.gasWarningData && this.gasWarningData.length > 0\r\n        ? this.gasWarningData\r\n        : [\r\n          { name: \"氧气O₂\", value: 1 },\r\n          { name: \"氢气H₂\", value: 2 },\r\n          { name: \"二氧化碳CO₂\", value: 1 },\r\n          { name: \"一氧化碳CO\", value: 3 },\r\n          { name: \"氨气NH₃\", value: 2 },\r\n          { name: \"甲烷\", value: 1 }\r\n        ];\r\n\r\n      // 提取x轴标签和数据\r\n      const xAxisData = chartData.map(item => item.name);\r\n      const seriesData = chartData.map(item => item.value);\r\n\r\n      const option = {\r\n        color: [\"#3398DB\"],\r\n        title: {\r\n          text: \"个\",\r\n          x: \"6%\",\r\n          y: \"8%\",\r\n          textStyle: {\r\n            color: \"#fff\",\r\n            fontSize: 14,\r\n          },\r\n        },\r\n        legend: {\r\n          data: [\"报警数\"],\r\n          top: \"8%\",\r\n          right: \"40px\",\r\n          itemWidth: 10,\r\n          itemHeight: 10,\r\n          textStyle: {\r\n            color: \"#fff\",\r\n            fontSize: 15\r\n          },\r\n        },\r\n        grid: {\r\n          top: \"18%\",\r\n          bottom: \"6%\",\r\n          left: \"2%\",\r\n          right: \"6%\",\r\n          containLabel: true,\r\n        },\r\n        xAxis: [\r\n          {\r\n            type: \"category\",\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              show: true,\r\n              rotate: 45,  // 设置标签旋转角度为45度\r\n              textStyle: {\r\n                color: \"#fff\", // 将 X 轴标签字体颜色设置为白色\r\n              },\r\n            },\r\n          },\r\n        ],\r\n        yAxis: [\r\n          {\r\n            axisTick: {\r\n              show:false,\r\n              alignWithLabel: false,\r\n            },\r\n            // min:1,\r\n            interval: 1,  // 固定刻度间隔为1\r\n            axisLabel: {\r\n              show: true,\r\n              textStyle: {\r\n                color: \"#fff\",\r\n                fontSize: 15\r\n                // 将 Y 轴标签字体颜色设置为白色\r\n              },\r\n            },\r\n          },\r\n        ],\r\n        series: [\r\n          {\r\n            name: \"报警数\",\r\n            type: \"bar\",\r\n            barWidth: \"40%\",\r\n            data: seriesData,\r\n            itemStyle: {\r\n              normal: {\r\n                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                  {\r\n                    offset: 0,\r\n                    color: \"#66C4FC\",\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: \"#66C4FC\",\r\n                  },\r\n                ]),\r\n                shadowColor: \"rgba(0, 0, 0, 0.1)\",\r\n                shadowBlur: 10,\r\n              },\r\n            },\r\n          },\r\n        ],\r\n      };\r\n\r\n      myChart.setOption(option);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.echart {\r\n  margin-left: 10px;\r\n  width: 95%;\r\n  // margin-top: 20px;\r\n  height: 320px;\r\n}\r\n</style>"]}]}