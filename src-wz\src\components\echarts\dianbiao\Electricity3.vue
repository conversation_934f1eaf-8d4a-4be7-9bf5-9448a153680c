<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'Electricity3',
  props: {
    electricityData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      defaultData: [
        { name: 'A区.1层.LAPYS-1', value: 120.5 },
        { name: 'A区.1层.LAPYS-2', value: 110.2 },
        { name: 'A区.2层.225', value: 95.8 },
        { name: 'A区.2层.231', value: 88.3 },
        { name: 'B区.1层.101', value: 82.6 },
        { name: 'B区.1层.102', value: 75.9 },
        { name: 'B区.2层.201', value: 68.4 },
        { name: 'C区.1层.301', value: 62.7 },
        { name: 'C区.1层.302', value: 55.1 },
        { name: 'C区.2层.401', value: 48.5 }
      ]
    };
  },
  watch: {
    electricityData: {
      handler(newData) {
        this.$nextTick(() => {
          if (newData && newData.length > 0) {
            this.processDataAndUpdateChart();
          } else {
            // 如果没有实际数据，使用默认数据
            this.updateChart(this.defaultData);
          }
        });
      },
      immediate: true
    }
  },
  methods: {
    processDataAndUpdateChart() {
      // 处理数据，按房间分组并计算总用电量
      const roomStats = {};
      this.electricityData.forEach(item => {
        if (!roomStats[item.zhaddress]) {
          roomStats[item.zhaddress] = 0;
        }
        roomStats[item.zhaddress] += parseFloat(item.ylvalue || 0);
      });

      // 转换为数组并排序
      const roomData = Object.entries(roomStats)
        .map(([name, value]) => ({ name, value: parseFloat(value.toFixed(2)) }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 10); // 只显示前10个房间

      this.updateChart(roomData.length > 0 ? roomData : this.defaultData);
    },

    updateChart(roomData) {
      this.$nextTick(() => {
        if (!this.chart && this.$refs.chartContainer) {
          this.chart = echarts.init(this.$refs.chartContainer);
        }

        if (this.chart) {
          const option = {
            tooltip: {
              show: true,
              trigger: 'item',
              backgroundColor: 'rgba(10, 48, 84, 0.95)',
              borderColor: '#1e415c',
              textStyle: {
                color: '#fff',
                fontSize: 14
              },
              formatter: function(params) {
                return `<div style="padding: 8px">
                  <div style="margin-bottom: 5px">房间：${params.name}</div>
                  <div style="color: #2cc1ff">用电量：${params.value} kwh</div>
                </div>`;
              }
            },
            grid: {
              top: '10%',
              left: '3%',
              right: '4%',
              bottom: '0%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: roomData.map(item => item.name),
              axisLabel: {
                color: '#fff',
                interval: 0,
                rotate: 35,
                formatter: function(value) {
                  return value.length > 9 ? value.substring(0, 8) + '...' : value;
                }
              },
              axisLine: {
                lineStyle: {
                  color: '#1e415c'
                }
              }
            },
            yAxis: {
              type: 'value',
              name: '用电量(kwh)',
              nameTextStyle: {
                color: '#fff'
              },
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                lineStyle: {
                  color: '#1e415c'
                }
              },
              splitLine: {
                lineStyle: {
                  color: '#1e415c',
                  opacity: 0.3
                }
              }
            },
            series: [{
              name: '用电量',
              type: 'bar',
              barWidth: '40%',
              data: roomData.map(item => ({
                value: item.value,
                name: item.name,
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#2cc1ff' },
                    { offset: 1, color: '#0a3054' }
                  ])
                }
              })),
              label: {
                show: true,
                position: 'top',
                color: '#fff',
                formatter: '{c} kwh'
              }
            }]
          };

          this.chart.setOption(option);
        }
      });
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 如果没有传入数据，使用默认数据
      if (!this.electricityData || this.electricityData.length === 0) {
        this.updateChart(this.defaultData);
      }
      window.addEventListener('resize', this.handleResize);
    });
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener('resize', this.handleResize);
  }
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 705px;
}
</style>