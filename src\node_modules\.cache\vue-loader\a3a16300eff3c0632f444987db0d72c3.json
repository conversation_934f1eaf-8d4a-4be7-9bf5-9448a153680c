{"remainingRequest": "E:\\svn\\SH-*************-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-*************-SFTianjinUniversityH5\\src\\src\\views\\tongji\\baojing.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\SH-*************-SFTianjinUniversityH5\\src\\src\\views\\tongji\\baojing.vue", "mtime": 1750744791852}, {"path": "E:\\svn\\SH-*************-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-*************-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-*************-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-*************-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\SH-*************-SFTianjinUniversityH5\\src\\src\\views\\tongji\\baojing.vue"], "names": [], "mappings": ";AAkEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;IAEV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACxD,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACxD,CAAC;QACH,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAC1D,CAAC;UACD;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;;UAEvD,CAAC;QACH,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3C,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3C,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAET,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACxD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;MACH,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC/C,CAAC;MACH,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnB,CAAC;EACH,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACf,CAAC,CAAC;;QAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAChB,CAAC;;UAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACf,CAAC;UACH,CAAC,CAAC;;UAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;gBACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1C;YACF;UACF,CAAC,CAAC;;UAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;cACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC;UACH,CAAC;;UAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC;;QAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC;;UAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;gBACvC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrB;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;;UAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;YAC1E,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;cACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACX,CAAC;;YAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC;UACH,CAAC,CAAC;;UAEF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE1C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;EACH,CAAC;AACH,CAAC", "file": "E:/svn/SH-*************-SFTianjinUniversityH5/src/src/views/tongji/baojing.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"contents\" v-if=\"isshow\">\r\n    <div class=\"toubu\">\r\n      <div style=\"margin-left: 20px; display: flex; align-items: center\" v-if=\"false\">\r\n        <div style=\"display: flex; width: 100%; align-items: center\">\r\n          <span class=\"sp\">当前位置：</span>\r\n          <el-select class=\"el-select\" v-model=\"selectvalue1\" placeholder=\"selectvalue1\"\r\n            style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n            <el-option v-for=\"item in options1\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n          <el-select class=\"el-select\" v-model=\"selectvalue2\" placeholder=\"selectvalue2\"\r\n            style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n            <el-option v-for=\"item in options2\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n          <el-select class=\"el-select\" v-model=\"selectvalue3\" placeholder=\"selectvalue3\"\r\n            style=\"width: 78px; height: 35.1px\" @change=\"handleChange\">\r\n            <el-option v-for=\"item in options3\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n        <img v-if=\"isshow\" class=\"img1sss\" @click=\"anniu()\" src=\"../../assets/image/table-x.png\" alt=\"\" />\r\n      </div>\r\n\r\n      <div class=\"all\">\r\n        <div class=\"all1\">\r\n          <Titles class=\"ltitle\" tit=\"环境报警统计\">\r\n\r\n            <div class=\"boxxx\" style=\"margin-top:30px;\">\r\n\r\n              <huanxing :warningData=\"warningStats\"></huanxing>\r\n            </div>\r\n\r\n          </Titles>\r\n          <Titles class=\"ltitle11\" tit=\"当月气体报警统计\">\r\n            <Electricity4 :gasWarningData=\"gasWarningData\"></Electricity4>\r\n            <!-- <div class=\"shinei\">\r\n              <Electricity4></Electricity4>\r\n            </div> -->\r\n          </Titles>\r\n        </div>\r\n        <!-- <div class=\"line1\"></div> -->\r\n        <div class=\"all2\">\r\n          <Titles class=\"ltitle1\" tit=\"传感器实时报警占比\">\r\n            <div class=\"shinei\">\r\n              <huanxing2 style=\"margin-top:30px;margin-bottom:26px\" :chartData=\"chartData\"></huanxing2>\r\n            </div>\r\n          </Titles>\r\n          <Titles class=\"ltitle211\" tit=\"新风设备报警占比\">\r\n            <div class=\"shinei\">\r\n              <huanxing2 style=\"margin-top:30px\" :chartData=\"chartDataxf\"></huanxing2>\r\n            </div>\r\n          </Titles>\r\n          <!-- <div>\r\n            <Titles class=\"ltitle1\" tit=\"设备购入时间\">\r\n              <div class=\"shinei\">\r\n                <Electricity3 :chartData=\"chartData3\"></Electricity3>\r\n              </div>\r\n            </Titles>\r\n          </div> -->\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport huanxing2 from \"@/components/fuyong/xiaobingtu.vue\";\r\nimport huanxing1 from \"@/components/fuyong/indexhuan.vue\";\r\nimport Electricity from \"@/components/echarts/dianbiao/biao1.vue\";\r\nimport biao1s from \"@/components/echarts/dianbiao/biao1s.vue\";\r\nimport biao1ss from \"@/components/echarts/dianbiao/biao1ss.vue\";\r\nimport Titles from \"@/components/common/Titles.vue\";\r\nimport echarts1 from \"@/components/echarts/bingjifang/echarts1.vue\";\r\nimport Electricity2 from \"@/components/echarts/dianbiao/Electricity2.vue\";\r\nimport Electricity3 from \"@/components/fuyong//zhexiantu.vue\";\r\nimport Electricity4 from \"@/components/gongqi/Electricity4.vue\";\r\nimport Electricity5 from \"@/components/echarts/dianbiao/Electricity5.vue\";\r\nimport Electricity6 from \"@/components/fuyong/Electricity6.vue\";\r\nimport Electricity7 from \"@/components/fuyong/Electricity7.vue\";\r\n// import Electricity7 from \"@/components/echarts/dianbiao/Electricity7.vue\";\r\nimport Electricity8 from \"@/components/fuyong/Electricity8.vue\";\r\nimport huanxing from \"@/components/echarts/huanxing.vue\";\r\nimport zhuzhuangtu from \"@/components/fuyong/zhuzhuangtu.vue\";\r\nimport echarts3 from \"@/components/echarts/kongya/echarts2.vue\";\r\nimport {\r\n  getDeviceData,\r\n  getDevicedetails,\r\n  getDeviceWarningList,\r\n} from \"@/api/device.js\";\r\nexport default {\r\n  components: {\r\n\r\n    echarts1,\r\n    echarts3,\r\n    huanxing2,\r\n    Titles,\r\n    Electricity,\r\n    Electricity2,\r\n    Electricity3,\r\n    Electricity4,\r\n    Electricity5,\r\n    Electricity6,\r\n    Electricity7,\r\n    Electricity8,\r\n    huanxing,\r\n    huanxing1,\r\n    biao1s,\r\n    biao1ss,\r\n    zhuzhuangtu\r\n  },\r\n  data() {\r\n    return {\r\n      warningStats: [],\r\n      chartData: {\r\n        value: [1, 1, 1, 1],\r\n        legend: [\r\n          \"温度传感器\",\r\n          \"湿度传感器\",\r\n          \"压差传感器\",\r\n          \"氧气传感器\",\r\n        ],\r\n      },\r\n      echartData2: {\r\n        unit: \"单位:℃\",\r\n        legend: [\"今日\", \"昨日\"],\r\n        xdata: [\r\n          \"1:00\",\r\n          \"3:00\",\r\n          \"5:00\",\r\n          \"7:00\",\r\n          \"9:00\",\r\n          \"11:00\",\r\n          \"13:00\",\r\n          \"15:00\",\r\n          \"17:00\",\r\n          \"19:00\",\r\n          \"21:00\",\r\n          \"23:00\",\r\n        ],\r\n        ydata: [\r\n          {\r\n            name: \"今日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [23, 22, 24, 23, 22, 21, 23, 23, 23, 24, 22, 23],\r\n          },\r\n          {\r\n            name: \"昨日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [21, 22, 22, 23, 21, 22, 22, 21, 23, 21, 23, 23],\r\n          },\r\n        ],\r\n      },\r\n      echartData3: {\r\n        unit: \"单位:%RH\",\r\n        legend: [\"今日\", \"昨日\"],\r\n        xdata: [\r\n          \"1:00\",\r\n          \"3:00\",\r\n          \"5:00\",\r\n          \"7:00\",\r\n          \"9:00\",\r\n          \"11:00\",\r\n          \"13:00\",\r\n          \"15:00\",\r\n          \"17:00\",\r\n          \"19:00\",\r\n          \"21:00\",\r\n          \"23:00\",\r\n        ],\r\n        ydata: [\r\n          {\r\n            name: \"今日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [43, 42, 44, 43, 45, 46, 42, 45, 42, 41.5, 42, 43],\r\n          },\r\n          {\r\n            name: \"昨日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [41, 42, 44, 44, 49, 47, 41, 44, 41, 43, 43, 46]\r\n\r\n          },\r\n        ],\r\n      },\r\n      chartDataxf: {\r\n        value: [2, 3, 2],\r\n        legend: [\r\n          \"空调\",\r\n          \"水泵\",\r\n          \"冷却塔\",\r\n        ],\r\n      },\r\n      chartData1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [720, 710, 730, 705, 715, 725],\r\n        yAxisdata2: [480, 490, 470, 495, 485, 475]\r\n      },\r\n      chartData2: {\r\n        title: [\" \", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [600, 580, 620, 590, 610, 605],\r\n        yAxisdata2: [300, 320, 280, 310, 290, 295]\r\n      },\r\n      chartData3: {\r\n        title: [\"仪器设备\", \"办公设备\"],\r\n        xAxisdata: [\r\n          \"10/16\",\r\n          \"10/17\",\r\n          \"10/18\",\r\n          \"10/19\",\r\n          \"10/20\",\r\n          \"10/21\",\r\n          \"10/22\",\r\n          \"10/23\",\r\n          \"10/24\",\r\n          \"10/25\",\r\n          \"10/26\",\r\n          \"10/27\",\r\n          \"10/28\",\r\n\r\n        ],\r\n        yAxisdata1: [4, 17, 5, 9, 6, 5, 0, 0, 12, 0, 4, 0, 1],\r\n        yAxisdata2: [14, 7, 2, 3, 16, 5, 0, 0, 2, 0, 13, 0, 0],\r\n      },\r\n      isshow: true,\r\n      options: [\r\n        {\r\n          value: \"总览\",\r\n          label: \"总览\",\r\n        },\r\n        {\r\n          value: \"能耗分析\",\r\n          label: \"能耗分析\",\r\n        },\r\n        {\r\n          value: \"能流分析\",\r\n          label: \"能流分析\",\r\n        },\r\n        {\r\n          value: \"设备状态\",\r\n          label: \"设备状态\",\r\n        },\r\n        {\r\n          value: \"一键抄表\",\r\n          label: \"一键抄表\",\r\n        },\r\n        {\r\n          value: \"费用管理\",\r\n          label: \"费用管理\",\r\n        },\r\n        {\r\n          value: \"碳排放管理\",\r\n          label: \"碳排放管理\",\r\n        },\r\n      ],\r\n      selectvalue2: \"B3\",\r\n      options2: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B3\",\r\n      options3: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue1: \"B3\",\r\n      options1: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B1栋\",\r\n      options4: [\r\n        {\r\n          value: \"B1栋\",\r\n          label: \"B1栋\",\r\n        },\r\n        {\r\n          value: \"B2栋\",\r\n          label: \"B2栋\",\r\n        },\r\n        {\r\n          value: \"B3栋\",\r\n          label: \"B3栋\",\r\n        },\r\n        {\r\n          value: \"B4栋\",\r\n          label: \"B4栋\",\r\n        },\r\n        {\r\n          value: \"W1栋\",\r\n          label: \"W1栋\",\r\n        },\r\n        {\r\n          value: \"W2栋\",\r\n          label: \"W2栋\",\r\n        },\r\n      ],\r\n      selectvalue4: \"B1栋\",\r\n      optionData: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      optionData1: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      // 气体报警统计数据，用于传递给Electricity4组件\r\n      gasWarningData: []\r\n    };\r\n  },\r\n  mounted() {\r\n    this.fetchWarningStats();\r\n    this.fetchgqwarn()\r\n    // 每30秒更新一次数据\r\n    setInterval(() => {\r\n      this.fetchWarningStats();\r\n    }, 30000);\r\n  },\r\n  methods: {\r\n    async fetchWarningStats() {\r\n      try {\r\n        const res = await getDeviceWarningList({\r\n          pageSize: 9999,\r\n          currentPage: 1,\r\n          hasFixed: \"N\",\r\n        });\r\n\r\n        if (res.code === 200 && res.rows) {\r\n          // 定义所有可能的报警类型及其阈值\r\n          const allWarningTypes = {\r\n            压力报警: 14,\r\n            氧气浓度报警: 48,\r\n            温度报警: 67,\r\n            湿度报警: 67,\r\n            // '气体泄漏报警': 50\r\n          };\r\n\r\n          // 统计各类型报警数量\r\n          const stats = {};\r\n          // 初始化所有报警类型的计数为0\r\n          Object.keys(allWarningTypes).forEach((type) => {\r\n            stats[type] = {\r\n              total: 0,\r\n              unresolved: 0,\r\n            };\r\n          });\r\n\r\n          // 统计实际数据\r\n          res.rows.forEach((item) => {\r\n            if (stats[item.warningCategory]) {\r\n              stats[item.warningCategory].total++;\r\n              if (item.status === \"N\") {\r\n                stats[item.warningCategory].unresolved++;\r\n              }\r\n            }\r\n          });\r\n\r\n          // 转换为图表所需格式\r\n          this.warningStats = Object.entries(stats).map(\r\n            ([category, count]) => ({\r\n              label: `${category}(${count.total}/${allWarningTypes[category]})`,\r\n              value: count.total,\r\n            })\r\n          );\r\n\r\n          console.log(this.warningStats, \"报警统计数据\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取报警统计数据失败:\", error);\r\n      }\r\n    },\r\n    async fetchgqwarn() {\r\n      try {\r\n        const res = await getDeviceWarningList({\r\n          pageSize: 9999,\r\n          currentPage: 1,\r\n          hasFixed: \"N\",\r\n          deviceTypes: 'GQXT',\r\n          deviceType: 'GQXT'\r\n        });\r\n\r\n        if (res.code === 200 && res.rows) {\r\n          console.log(res, \"报警统计数据\");\r\n\r\n          // 定义需要统计的气体类型\r\n          const gasTypes = {\r\n            '一氧化碳': 0,\r\n            '二氧化碳': 0,\r\n            '氢气': 0,\r\n            '氧气': 0,\r\n            '氨气': 0,\r\n            '甲烷': 0\r\n          };\r\n\r\n          // 统计每种气体的报警数量\r\n          res.rows.forEach(item => {\r\n            if (item.deviceName) {\r\n              Object.keys(gasTypes).forEach(gasType => {\r\n                if (item.deviceName.includes(gasType)) {\r\n                  gasTypes[gasType]++;\r\n                }\r\n              });\r\n            }\r\n          });\r\n\r\n          // 转换为Electricity4组件所需的数据格式\r\n          const electricity4Data = Object.entries(gasTypes).map(([gasType, count]) => {\r\n            // 根据气体类型映射到Electricity4组件中的显示名称\r\n            const displayNames = {\r\n              '氧气': '氧气O₂',\r\n              '氢气': '氢气H₂',\r\n              '二氧化碳': '二氧化碳CO₂',\r\n              '一氧化碳': '一氧化碳CO',\r\n              '氨气': '氨气NH₃',\r\n              '甲烷': '甲烷'\r\n            };\r\n\r\n            return {\r\n              name: displayNames[gasType] || gasType,\r\n              value: count\r\n            };\r\n          });\r\n\r\n          console.log('气体报警统计数据:', electricity4Data);\r\n\r\n          // 将数据存储到data中，供Electricity4组件使用\r\n          this.gasWarningData = electricity4Data;\r\n\r\n          return electricity4Data;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取报警统计数据失败:\", error);\r\n      }\r\n    },\r\n    anniu() {\r\n      this.isshow = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.zhuzhuangtu {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.shbei {\r\n  margin-top: 10px;\r\n\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  width: 411px;\r\n  height: 70px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n\r\n  .item {\r\n    display: flex;\r\n\r\n    img {\r\n      width: 58px;\r\n      height: 56px;\r\n    }\r\n\r\n    .numlist {\r\n      margin-left: 2px;\r\n\r\n      .it1 {\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: bold;\r\n        font-size: 25px;\r\n        color: #ffffff;\r\n      }\r\n\r\n      .it2 {\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #00ffff;\r\n      }\r\n\r\n      .it3 {\r\n        color: #00ffcc;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.boxxx {\r\n  margin-left: 116px;\r\n  margin-bottom: 18px;\r\n  // background: url(\"../assets/image/zuoshang1.png\");\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n\r\n  width: 350px;\r\n  height: 284px;\r\n}\r\n\r\n.all {\r\n  display: flex;\r\n  flex-direction: row;\r\n  margin-top: 5px;\r\n\r\n  .zong {\r\n    display: flex;\r\n    flex-direction: row;\r\n    margin-top: 10px;\r\n\r\n    .echart1,\r\n    .echart2 {\r\n      flex: 1;\r\n\r\n      .center {\r\n        margin-top: -24px;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: 400;\r\n        font-size: 17px;\r\n        color: #00ffb6;\r\n        text-align: center;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .btn {\r\n        width: 133px;\r\n        height: 31px;\r\n        border: 1px solid #2d6cb0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #ffffff;\r\n        border-radius: 30px;\r\n        margin-left: 7%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ltitle {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle1 {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle11 {\r\n    width: 90%;\r\n    margin-top: 80px;\r\n  }\r\n\r\n  .ltitle211 {\r\n    // width: 90%;\r\n    margin-top: 80px;\r\n  }\r\n\r\n  .line1 {\r\n    width: 2px;\r\n    height: 823px;\r\n    opacity: 0.64;\r\n    background-color: #204964;\r\n  }\r\n\r\n  .all1 {\r\n    flex: 1;\r\n\r\n    .nenghao {\r\n      width: 227px;\r\n      height: 173px;\r\n      background: url(\"../../assets/image/nenghao.png\");\r\n      background-size: 100% 100%;\r\n      margin-left: 120px;\r\n      margin-top: 21px;\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      line-height: 213px;\r\n    }\r\n\r\n    .nhp {\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 500;\r\n      font-size: 30px;\r\n      color: #2cc1ff;\r\n      margin-top: 8px;\r\n    }\r\n\r\n    .nh {\r\n      margin-left: 24px;\r\n      margin-top: 5px;\r\n      width: 423px;\r\n      height: 93px;\r\n      border: 1px solid #364d5a;\r\n      background-size: 100% 100%;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      padding-left: 42px;\r\n\r\n      .nhimg {\r\n        width: 96.6px;\r\n        height: 70px;\r\n      }\r\n\r\n      .nhtit {\r\n        width: 148px;\r\n        margin-left: 10px;\r\n        margin-top: 10px;\r\n\r\n        .p11 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #7acfff;\r\n        }\r\n\r\n        .p12 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #ffa170;\r\n        }\r\n\r\n        .p2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .nhtit1 {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-left: 35px;\r\n\r\n        .nhimg1 {\r\n          width: 16px;\r\n          height: 20px;\r\n        }\r\n\r\n        .pp1 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #0df29b;\r\n        }\r\n\r\n        .pp2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffa170;\r\n        }\r\n      }\r\n\r\n      .nht {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .pp {\r\n          margin-left: 35px;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n\r\n          color: #cccccc;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all2 {\r\n    margin-left: 38px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .shinei {\r\n      .itemshei {\r\n        display: flex;\r\n        justify-content: space-around;\r\n\r\n        .nenghaos {\r\n          width: 227px;\r\n          height: 173px;\r\n          background: url(\"../../assets/image/nenghao.png\");\r\n          background-size: 100% 100%;\r\n          text-align: center;\r\n          margin-left: 10px;\r\n          margin-top: 23px;\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #ffffff;\r\n          line-height: 144px;\r\n        }\r\n\r\n        .nhps {\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 500;\r\n          font-size: 21px;\r\n          color: #2cc1ff;\r\n          margin-top: 8px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all3 {\r\n    flex: 668;\r\n    margin-left: 45px;\r\n  }\r\n}\r\n\r\n.shinei {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shuantitle {\r\n  width: 100%;\r\n  display: flex;\r\n  margin-top: 10px;\r\n\r\n  .title {\r\n    width: 95%;\r\n    background: url(\"../../assets/image/title.png\");\r\n    background-size: 100% 100%;\r\n\r\n    height: 25px;\r\n    font-family: Source Han Sans SC;\r\n    font-weight: 400;\r\n    font-size: 25px;\r\n    color: #ffffff;\r\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\r\n    font-style: italic;\r\n    text-align: left;\r\n    line-height: 4px;\r\n    padding-left: 33px;\r\n  }\r\n}\r\n\r\n.nenghao {\r\n  width: 167px;\r\n  height: 113px;\r\n  background: url(\"../../assets/image/nenghao.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin-left: 83px;\r\n  margin-top: 63px;\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #ffffff;\r\n  line-height: 144px;\r\n}\r\n\r\n.nhp {\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 500;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n  margin-top: 8px;\r\n}\r\n\r\n.contents {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: url(\"../../assets/image/zichanbeijin.png\");\r\n  background-size: 100% 100%;\r\n  width: 1863px;\r\n  height: 868px;\r\n  z-index: 99999;\r\n  padding-left: 34px;\r\n  padding-right: 22px;\r\n  padding-top: 21px;\r\n}\r\n\r\n.toubu {\r\n  width: 100%;\r\n\r\n  position: relative;\r\n}\r\n\r\n.el-select {\r\n  margin-top: -1px;\r\n  margin-left: 10px;\r\n  background: #00203d;\r\n  border-radius: 3px;\r\n  border: 1px solid #3e89db;\r\n\r\n  /deep/.el-select__wrapper {\r\n    background: #00203d !important;\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper .is-hovering:not {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper:hover {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__placeholder.is-transparent {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select__placeholder {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select-dropdown__item.is-hovering {\r\n    background-color: #2cc1ff !important;\r\n  }\r\n}\r\n\r\n.sp {\r\n  margin-top: -5px;\r\n  margin-left: 12px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: bold;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n}\r\n\r\n.img1sss {\r\n  cursor: pointer;\r\n  width: 15px;\r\n  height: 15px;\r\n}\r\n</style>"]}]}