<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>嵌套测试</title>
    <style>
      .content {
        /* position: fixed; */
        top: 30px;
        right: 40px;
        z-index: 999;

        color: rgb(22, 22, 22);
      }
    </style>
  </head>
  <body>
    <div>
      <button onclick="seed()">显示图标</button>
      <button onclick="reset()">复位</button>
      <div id="message-container" class="content"></div>
      <iframe
        id="ifram"
        width="1920px"
        height="1080px"
        src="http://127.0.0.1:5500/SH-202401026-0162-DH3DEMG-Tpl/tianhua/tiles.html?model=edit"
        frameborder="0"
      ></iframe>
      <!-- src地址的后面带model=edit 进入编辑模式可以点击获取点位  不带则隐藏编辑模式 -->
    </div>
    <script>
      //接受来自3d的信息
      window.addEventListener("message", function (event) {
        if (event.data && event.data.type == "iframedata") {
          console.log(event.data, "嵌入的页面发送的信息");
          const messageContainer = document.getElementById("message-container");

          messageContainer.innerHTML = ""; // 清空之前的消息
          const messageElement = document.createElement("p");
          messageElement.textContent = JSON.stringify(
            event.data.param.lablelist
          );
          messageContainer.appendChild(messageElement);
        }
      });
      let lablelist = [
        {
          id: "0001",
          name: "展厅1",
          url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E5%B1%95%E5%8E%85.png",
          point: {
            //位置
            x: 53.00671301129224,
            y: -8.831298828125,
            z: 81.97537288268227,
          },
          pos: {
            //视角
            x: 52.06677960757651,
            y: 43.85028590019469,
            z: 226.55780077342942,
          },
          tar: {
            //目标点
            x: 74.79918657102358,
            y: -58.29177585394653,
            z: 20.375782030684935,
          },
        },
        {
          id: "0002",
          name: "休息区",

          url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E4%BC%91%E6%81%AF%E5%8C%BA.png",
          point: {
            x: 252.4385189744658,
            y: -8.831298828125,
            z: 38.47656350624537,
          },
          pos: {
            x: 252.08595213284485,
            y: 40.372876249411675,
            z: -63.658095377412835,
          },
          tar: {
            x: 252.13835759129057,
            y: 2.337614165410212,
            z: 5.289834480945036,
          },
        },
        {
          id: "0003",
          name: "中庭",
          url: "https://3d.dddtask.cn/enginner-xufeng/dh/image/%E4%B8%AD%E5%BA%AD.png",
          point: {
            x: -103.93194397506923,
            y: -8.831298828125,
            z: 161.73958946711733,
          },
          pos: {
            x: -73.89839853251614,
            y: 56.92272834545296,
            z: 33.7387897222155,
          },
          tar: {
            x: -104.9455295520573,
            y: -36.200176292926734,
            z: 161.9405406399116,
          },
        },
      ];
      function seed() {
        const frame = document.getElementById("ifram");
        frame.contentWindow.postMessage(
          {
            data: {
              type: "setFloorData",
              info: {
                roomData: lablelist, //对应图标等信息
              },
            },
          },
          "*"
        );
      }
      function reset() {
        const frame = document.getElementById("ifram");
        frame.contentWindow.postMessage(
          {
            data: {
              type: "reset",
            },
          },
          "*"
        );
      }
    </script>
  </body>
</html>
