<template>
  <div class="zong">
    <el-table
      :data="tableData"
      style="width: 100%"
      :header-cell-style="headerCellStyle"
      :row-class-name="tableRowClassName"
    >
      <el-table-column prop="serialNumber" label="变压器编号"></el-table-column>
      <el-table-column prop="modelCode" label="电表"></el-table-column>
      <el-table-column prop="totalPrice" label="规格"></el-table-column>
      <el-table-column prop="amount" label="倍率"></el-table-column>
      <el-table-column prop="unitPrice" label="服务分区"></el-table-column>
      <el-table-column prop="totalPrice" label="能耗类型"></el-table-column>
      <el-table-column prop="totalPrice" label="部门"></el-table-column>
      <el-table-column prop="totalPrice" label="用量"></el-table-column>
      <el-table-column prop="totalPrice" label="起始读数"></el-table-column>
      <!-- Add more columns as needed -->
    </el-table>

    <el-pagination
      small
      background
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage4"
      :page-sizes="[100, 200, 300, 400]"
      :page-size="100"
      layout="total, sizes, prev, pager, next, jumper"
      :total="400"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentPage4: 4,
      tableData: [
        {
          serialNumber: "B1-1#",
          modelCode: "F1-B11-1",
          specification: "A规格标准",
          amount: "800",
          unitPrice: "D1单价标准",
          totalPrice: "0000.0",
          // Add more fields as needed
        },
        {
          serialNumber: "B1-1#",
          modelCode: "F1-B11-1",
          specification: "A规格标准",
          amount: "800",
          unitPrice: "D1单价标准",
          totalPrice: "0000.0",
          // Add more fields as needed
        },
        {
          serialNumber: "B1-1#",
          modelCode: "F1-B11-1",
          specification: "A规格标准",
          amount: "800",
          unitPrice: "D1单价标准",
          totalPrice: "0000.0",
          // Add more fields as needed
        },
        {
          serialNumber: "B1-1#",
          modelCode: "F1-B11-1",
          specification: "A规格标准",
          amount: "800",
          unitPrice: "D1单价标准",
          totalPrice: "0000.0",
          // Add more fields as needed
        },
        {
          serialNumber: "B1-1#",
          modelCode: "F1-B11-1",
          specification: "A规格标准",
          amount: "800",
          unitPrice: "D1单价标准",
          totalPrice: "0000.0",
          // Add more fields as needed
        },
        {
          serialNumber: "B1-1#",
          modelCode: "F1-B11-1",
          specification: "A规格标准",
          amount: "800",
          unitPrice: "D1单价标准",
          totalPrice: "0000.0",
          // Add more fields as needed
        },
        {
          serialNumber: "B1-1#",
          modelCode: "F1-B11-1",
          specification: "A规格标准",
          amount: "800",
          unitPrice: "D1单价标准",
          totalPrice: "0000.0",
          // Add more fields as needed
        },
        {
          serialNumber: "B1-1#",
          modelCode: "F1-B11-1",
          specification: "A规格标准",
          amount: "800",
          unitPrice: "D1单价标准",
          totalPrice: "0000.0",
          // Add more fields as needed
        },
        {
          serialNumber: "B1-1#",
          modelCode: "F1-B11-1",
          specification: "A规格标准",
          amount: "800",
          unitPrice: "D1单价标准",
          totalPrice: "0000.0",
          // Add more fields as needed
        },
      ],
      headerCellStyle: {
        backgroundColor: "#122544",
        fontFamily: "HYQiHei",
        fontWeight: "normal",
        fontSize: "13px",
        color: "#FFFFFF",
      },
    };
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      return rowIndex % 2 === 0 ? "warning-row" : "success-row";
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
  },
};
</script>
 <style lang="less" >
.el-table .warning-row {
  background: #182e52;
  color: #fff;
}

.el-table .success-row {
  background: #122544;
  color: #fff;
}
.el-table .el-table__body tr td {
  border-color: #779fee;
}
/* 去除整个表格的边框 */
.el-table {
  border: none !important;
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

/* 去除表头、表体和表尾的所有单元格边框 */
.el-table th,
.el-table td,
.el-table thead th,
.el-table tbody td,
.el-table tfoot td {
  border: none !important;
}

/* 针对行的底部边框 */
.el-table tr {
  border-bottom: none !important;
}

/* 针对表体的边框 */
.el-table tbody {
  border: none !important;
}

/* 如果边框是由伪元素添加的，去除表格所有伪元素的边框 */
.el-table::before,
.el-table::after,
.el-table *::before,
.el-table *::after {
  content: none !important;
  border: none !important;
  box-shadow: none !important; /* 如果边框是由阴影造成的 */
}

/* 有时候表格的外层容器也可能设置了边框 */
.el-table__wrapper,
.el-table__outer {
  border: none !important;
  box-shadow: none !important; /* 如果边框是由阴影造成的 */
} /* 鼠标悬停时更改整行文本颜色 */
.el-table .el-table__body tr:hover {
  color: black !important;
}

/* 或者，鼠标悬停时只更改单个单元格内文本的颜色 */
.el-table .el-table__body td:hover {
  color: black !important;
}

.pagination {
  margin-top: 35px;
}

.zong {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.el-pagination__total,
.el-pagination__jump {
  color: #fff !important;
}
</style>
