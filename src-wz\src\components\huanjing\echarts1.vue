<template>
  <div class="echart" ref="echart"></div>
</template>
    
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);

      app.title = "";

      const option = {
        //     legend: {
        //   textStyle: {
        //                 color:"#fff",
        //                 fontSize:14
        //             },
        //         top: '1%',
        //     right:"1%",
        //         data: ['未优化用电量', 'AI优化用电量'],
        //     },

        color: ["#3398DB"],
        title: {
          text: "单位：台",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        legend: {
          itemGap: 16,
          itemWidth: 13,
          itemHeight: 13,
          top: 0,
          right: "1%",
          textStyle: { fontSize: 16, color: "#fff" },
          data: ["运行总数","故障总数", ],
        },
        grid: {
          top: "18%",
          bottom: "0%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: ["温度传感器", "湿度传感器", "压差传感器","氧气传感器"],

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
                fontSize: 14,
              },
            },
          },
        ],
        yAxis: [
          {
            axisTick: {
              alignWithLabel: true,
            },

            axisLabel: {       
              show: true,
              textStyle: {
                color: "#fff", // 将 Y 轴标签字体颜色设置为白色
                fontSize: 16,
              },
            },
          },
        ],

        series: [
          
          {
            name: "运行总数",
            type: "bar",
            barWidth: "20%",
            data: [66, 66, 47, 14,],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#01C8AE",
                  },
                  {
                    offset: 1,
                    color: "#01C8AE",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
          {
            name: "故障总数",
            type: "bar",
            barWidth: "20%",
          
            data: [1, 1, 1, 1, 3, 3],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#FFFF33", 
                  },
                  {
                    offset: 1,
                    color: "#FFFF33",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
<style lang="less" scoped>
.echart {
  width: 455px;
  height: 320px;
}


</style>