{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\tongji\\baojing.vue?vue&type=style&index=0&id=9b3784f4&lang=less&scoped=true", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\tongji\\baojing.vue", "mtime": 1750744791852}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\tongji\\baojing.vue"], "names": [], "mappings": ";AA4eA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE7B,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEb,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACd;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEhB,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;IACF;EACF;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACf;;AAEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEf,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACjB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB;;IAEA,CAAC,CAAC,CAAC,EAAE;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjB;;IAEA,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACd;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACd;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;;QAEA,CAAC,CAAC,CAAC,EAAE;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;;MAEA,CAAC,CAAC,CAAC,EAAE;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtB,CAAC,CAAC,EAAE;UACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,EAAE;IACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACd;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACpB;AACF;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,EAAE;EACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACnB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtC;AACF;;AAEA,CAAC,CAAC,EAAE;EACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChB;;AAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACd", "file": "E:/svn/SH-20240918-0186-SFTianjinUniversityH5/src/src/views/tongji/baojing.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"contents\" v-if=\"isshow\">\r\n    <div class=\"toubu\">\r\n      <div style=\"margin-left: 20px; display: flex; align-items: center\" v-if=\"false\">\r\n        <div style=\"display: flex; width: 100%; align-items: center\">\r\n          <span class=\"sp\">当前位置：</span>\r\n          <el-select class=\"el-select\" v-model=\"selectvalue1\" placeholder=\"selectvalue1\"\r\n            style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n            <el-option v-for=\"item in options1\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n          <el-select class=\"el-select\" v-model=\"selectvalue2\" placeholder=\"selectvalue2\"\r\n            style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n            <el-option v-for=\"item in options2\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n          <el-select class=\"el-select\" v-model=\"selectvalue3\" placeholder=\"selectvalue3\"\r\n            style=\"width: 78px; height: 35.1px\" @change=\"handleChange\">\r\n            <el-option v-for=\"item in options3\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n        <img v-if=\"isshow\" class=\"img1sss\" @click=\"anniu()\" src=\"../../assets/image/table-x.png\" alt=\"\" />\r\n      </div>\r\n\r\n      <div class=\"all\">\r\n        <div class=\"all1\">\r\n          <Titles class=\"ltitle\" tit=\"环境报警统计\">\r\n\r\n            <div class=\"boxxx\" style=\"margin-top:30px;\">\r\n\r\n              <huanxing :warningData=\"warningStats\"></huanxing>\r\n            </div>\r\n\r\n          </Titles>\r\n          <Titles class=\"ltitle11\" tit=\"当月气体报警统计\">\r\n            <Electricity4 :gasWarningData=\"gasWarningData\"></Electricity4>\r\n            <!-- <div class=\"shinei\">\r\n              <Electricity4></Electricity4>\r\n            </div> -->\r\n          </Titles>\r\n        </div>\r\n        <!-- <div class=\"line1\"></div> -->\r\n        <div class=\"all2\">\r\n          <Titles class=\"ltitle1\" tit=\"传感器实时报警占比\">\r\n            <div class=\"shinei\">\r\n              <huanxing2 style=\"margin-top:30px;margin-bottom:26px\" :chartData=\"chartData\"></huanxing2>\r\n            </div>\r\n          </Titles>\r\n          <Titles class=\"ltitle211\" tit=\"新风设备报警占比\">\r\n            <div class=\"shinei\">\r\n              <huanxing2 style=\"margin-top:30px\" :chartData=\"chartDataxf\"></huanxing2>\r\n            </div>\r\n          </Titles>\r\n          <!-- <div>\r\n            <Titles class=\"ltitle1\" tit=\"设备购入时间\">\r\n              <div class=\"shinei\">\r\n                <Electricity3 :chartData=\"chartData3\"></Electricity3>\r\n              </div>\r\n            </Titles>\r\n          </div> -->\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport huanxing2 from \"@/components/fuyong/xiaobingtu.vue\";\r\nimport huanxing1 from \"@/components/fuyong/indexhuan.vue\";\r\nimport Electricity from \"@/components/echarts/dianbiao/biao1.vue\";\r\nimport biao1s from \"@/components/echarts/dianbiao/biao1s.vue\";\r\nimport biao1ss from \"@/components/echarts/dianbiao/biao1ss.vue\";\r\nimport Titles from \"@/components/common/Titles.vue\";\r\nimport echarts1 from \"@/components/echarts/bingjifang/echarts1.vue\";\r\nimport Electricity2 from \"@/components/echarts/dianbiao/Electricity2.vue\";\r\nimport Electricity3 from \"@/components/fuyong//zhexiantu.vue\";\r\nimport Electricity4 from \"@/components/gongqi/Electricity4.vue\";\r\nimport Electricity5 from \"@/components/echarts/dianbiao/Electricity5.vue\";\r\nimport Electricity6 from \"@/components/fuyong/Electricity6.vue\";\r\nimport Electricity7 from \"@/components/fuyong/Electricity7.vue\";\r\n// import Electricity7 from \"@/components/echarts/dianbiao/Electricity7.vue\";\r\nimport Electricity8 from \"@/components/fuyong/Electricity8.vue\";\r\nimport huanxing from \"@/components/echarts/huanxing.vue\";\r\nimport zhuzhuangtu from \"@/components/fuyong/zhuzhuangtu.vue\";\r\nimport echarts3 from \"@/components/echarts/kongya/echarts2.vue\";\r\nimport {\r\n  getDeviceData,\r\n  getDevicedetails,\r\n  getDeviceWarningList,\r\n} from \"@/api/device.js\";\r\nexport default {\r\n  components: {\r\n\r\n    echarts1,\r\n    echarts3,\r\n    huanxing2,\r\n    Titles,\r\n    Electricity,\r\n    Electricity2,\r\n    Electricity3,\r\n    Electricity4,\r\n    Electricity5,\r\n    Electricity6,\r\n    Electricity7,\r\n    Electricity8,\r\n    huanxing,\r\n    huanxing1,\r\n    biao1s,\r\n    biao1ss,\r\n    zhuzhuangtu\r\n  },\r\n  data() {\r\n    return {\r\n      warningStats: [],\r\n      chartData: {\r\n        value: [1, 1, 1, 1],\r\n        legend: [\r\n          \"温度传感器\",\r\n          \"湿度传感器\",\r\n          \"压差传感器\",\r\n          \"氧气传感器\",\r\n        ],\r\n      },\r\n      echartData2: {\r\n        unit: \"单位:℃\",\r\n        legend: [\"今日\", \"昨日\"],\r\n        xdata: [\r\n          \"1:00\",\r\n          \"3:00\",\r\n          \"5:00\",\r\n          \"7:00\",\r\n          \"9:00\",\r\n          \"11:00\",\r\n          \"13:00\",\r\n          \"15:00\",\r\n          \"17:00\",\r\n          \"19:00\",\r\n          \"21:00\",\r\n          \"23:00\",\r\n        ],\r\n        ydata: [\r\n          {\r\n            name: \"今日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [23, 22, 24, 23, 22, 21, 23, 23, 23, 24, 22, 23],\r\n          },\r\n          {\r\n            name: \"昨日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [21, 22, 22, 23, 21, 22, 22, 21, 23, 21, 23, 23],\r\n          },\r\n        ],\r\n      },\r\n      echartData3: {\r\n        unit: \"单位:%RH\",\r\n        legend: [\"今日\", \"昨日\"],\r\n        xdata: [\r\n          \"1:00\",\r\n          \"3:00\",\r\n          \"5:00\",\r\n          \"7:00\",\r\n          \"9:00\",\r\n          \"11:00\",\r\n          \"13:00\",\r\n          \"15:00\",\r\n          \"17:00\",\r\n          \"19:00\",\r\n          \"21:00\",\r\n          \"23:00\",\r\n        ],\r\n        ydata: [\r\n          {\r\n            name: \"今日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [43, 42, 44, 43, 45, 46, 42, 45, 42, 41.5, 42, 43],\r\n          },\r\n          {\r\n            name: \"昨日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [41, 42, 44, 44, 49, 47, 41, 44, 41, 43, 43, 46]\r\n\r\n          },\r\n        ],\r\n      },\r\n      chartDataxf: {\r\n        value: [2, 3, 2],\r\n        legend: [\r\n          \"空调\",\r\n          \"水泵\",\r\n          \"冷却塔\",\r\n        ],\r\n      },\r\n      chartData1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [720, 710, 730, 705, 715, 725],\r\n        yAxisdata2: [480, 490, 470, 495, 485, 475]\r\n      },\r\n      chartData2: {\r\n        title: [\" \", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [600, 580, 620, 590, 610, 605],\r\n        yAxisdata2: [300, 320, 280, 310, 290, 295]\r\n      },\r\n      chartData3: {\r\n        title: [\"仪器设备\", \"办公设备\"],\r\n        xAxisdata: [\r\n          \"10/16\",\r\n          \"10/17\",\r\n          \"10/18\",\r\n          \"10/19\",\r\n          \"10/20\",\r\n          \"10/21\",\r\n          \"10/22\",\r\n          \"10/23\",\r\n          \"10/24\",\r\n          \"10/25\",\r\n          \"10/26\",\r\n          \"10/27\",\r\n          \"10/28\",\r\n\r\n        ],\r\n        yAxisdata1: [4, 17, 5, 9, 6, 5, 0, 0, 12, 0, 4, 0, 1],\r\n        yAxisdata2: [14, 7, 2, 3, 16, 5, 0, 0, 2, 0, 13, 0, 0],\r\n      },\r\n      isshow: true,\r\n      options: [\r\n        {\r\n          value: \"总览\",\r\n          label: \"总览\",\r\n        },\r\n        {\r\n          value: \"能耗分析\",\r\n          label: \"能耗分析\",\r\n        },\r\n        {\r\n          value: \"能流分析\",\r\n          label: \"能流分析\",\r\n        },\r\n        {\r\n          value: \"设备状态\",\r\n          label: \"设备状态\",\r\n        },\r\n        {\r\n          value: \"一键抄表\",\r\n          label: \"一键抄表\",\r\n        },\r\n        {\r\n          value: \"费用管理\",\r\n          label: \"费用管理\",\r\n        },\r\n        {\r\n          value: \"碳排放管理\",\r\n          label: \"碳排放管理\",\r\n        },\r\n      ],\r\n      selectvalue2: \"B3\",\r\n      options2: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B3\",\r\n      options3: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue1: \"B3\",\r\n      options1: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B1栋\",\r\n      options4: [\r\n        {\r\n          value: \"B1栋\",\r\n          label: \"B1栋\",\r\n        },\r\n        {\r\n          value: \"B2栋\",\r\n          label: \"B2栋\",\r\n        },\r\n        {\r\n          value: \"B3栋\",\r\n          label: \"B3栋\",\r\n        },\r\n        {\r\n          value: \"B4栋\",\r\n          label: \"B4栋\",\r\n        },\r\n        {\r\n          value: \"W1栋\",\r\n          label: \"W1栋\",\r\n        },\r\n        {\r\n          value: \"W2栋\",\r\n          label: \"W2栋\",\r\n        },\r\n      ],\r\n      selectvalue4: \"B1栋\",\r\n      optionData: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      optionData1: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      // 气体报警统计数据，用于传递给Electricity4组件\r\n      gasWarningData: []\r\n    };\r\n  },\r\n  mounted() {\r\n    this.fetchWarningStats();\r\n    this.fetchgqwarn()\r\n    // 每30秒更新一次数据\r\n    setInterval(() => {\r\n      this.fetchWarningStats();\r\n    }, 30000);\r\n  },\r\n  methods: {\r\n    async fetchWarningStats() {\r\n      try {\r\n        const res = await getDeviceWarningList({\r\n          pageSize: 9999,\r\n          currentPage: 1,\r\n          hasFixed: \"N\",\r\n        });\r\n\r\n        if (res.code === 200 && res.rows) {\r\n          // 定义所有可能的报警类型及其阈值\r\n          const allWarningTypes = {\r\n            压力报警: 14,\r\n            氧气浓度报警: 48,\r\n            温度报警: 67,\r\n            湿度报警: 67,\r\n            // '气体泄漏报警': 50\r\n          };\r\n\r\n          // 统计各类型报警数量\r\n          const stats = {};\r\n          // 初始化所有报警类型的计数为0\r\n          Object.keys(allWarningTypes).forEach((type) => {\r\n            stats[type] = {\r\n              total: 0,\r\n              unresolved: 0,\r\n            };\r\n          });\r\n\r\n          // 统计实际数据\r\n          res.rows.forEach((item) => {\r\n            if (stats[item.warningCategory]) {\r\n              stats[item.warningCategory].total++;\r\n              if (item.status === \"N\") {\r\n                stats[item.warningCategory].unresolved++;\r\n              }\r\n            }\r\n          });\r\n\r\n          // 转换为图表所需格式\r\n          this.warningStats = Object.entries(stats).map(\r\n            ([category, count]) => ({\r\n              label: `${category}(${count.total}/${allWarningTypes[category]})`,\r\n              value: count.total,\r\n            })\r\n          );\r\n\r\n          console.log(this.warningStats, \"报警统计数据\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取报警统计数据失败:\", error);\r\n      }\r\n    },\r\n    async fetchgqwarn() {\r\n      try {\r\n        const res = await getDeviceWarningList({\r\n          pageSize: 9999,\r\n          currentPage: 1,\r\n          hasFixed: \"N\",\r\n          deviceTypes: 'GQXT',\r\n          deviceType: 'GQXT'\r\n        });\r\n\r\n        if (res.code === 200 && res.rows) {\r\n          console.log(res, \"报警统计数据\");\r\n\r\n          // 定义需要统计的气体类型\r\n          const gasTypes = {\r\n            '一氧化碳': 0,\r\n            '二氧化碳': 0,\r\n            '氢气': 0,\r\n            '氧气': 0,\r\n            '氨气': 0,\r\n            '甲烷': 0\r\n          };\r\n\r\n          // 统计每种气体的报警数量\r\n          res.rows.forEach(item => {\r\n            if (item.deviceName) {\r\n              Object.keys(gasTypes).forEach(gasType => {\r\n                if (item.deviceName.includes(gasType)) {\r\n                  gasTypes[gasType]++;\r\n                }\r\n              });\r\n            }\r\n          });\r\n\r\n          // 转换为Electricity4组件所需的数据格式\r\n          const electricity4Data = Object.entries(gasTypes).map(([gasType, count]) => {\r\n            // 根据气体类型映射到Electricity4组件中的显示名称\r\n            const displayNames = {\r\n              '氧气': '氧气O₂',\r\n              '氢气': '氢气H₂',\r\n              '二氧化碳': '二氧化碳CO₂',\r\n              '一氧化碳': '一氧化碳CO',\r\n              '氨气': '氨气NH₃',\r\n              '甲烷': '甲烷'\r\n            };\r\n\r\n            return {\r\n              name: displayNames[gasType] || gasType,\r\n              value: count\r\n            };\r\n          });\r\n\r\n          console.log('气体报警统计数据:', electricity4Data);\r\n\r\n          // 将数据存储到data中，供Electricity4组件使用\r\n          this.gasWarningData = electricity4Data;\r\n\r\n          return electricity4Data;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取报警统计数据失败:\", error);\r\n      }\r\n    },\r\n    anniu() {\r\n      this.isshow = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.zhuzhuangtu {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.shbei {\r\n  margin-top: 10px;\r\n\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  width: 411px;\r\n  height: 70px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n\r\n  .item {\r\n    display: flex;\r\n\r\n    img {\r\n      width: 58px;\r\n      height: 56px;\r\n    }\r\n\r\n    .numlist {\r\n      margin-left: 2px;\r\n\r\n      .it1 {\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: bold;\r\n        font-size: 25px;\r\n        color: #ffffff;\r\n      }\r\n\r\n      .it2 {\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #00ffff;\r\n      }\r\n\r\n      .it3 {\r\n        color: #00ffcc;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.boxxx {\r\n  margin-left: 116px;\r\n  margin-bottom: 18px;\r\n  // background: url(\"../assets/image/zuoshang1.png\");\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n\r\n  width: 350px;\r\n  height: 284px;\r\n}\r\n\r\n.all {\r\n  display: flex;\r\n  flex-direction: row;\r\n  margin-top: 5px;\r\n\r\n  .zong {\r\n    display: flex;\r\n    flex-direction: row;\r\n    margin-top: 10px;\r\n\r\n    .echart1,\r\n    .echart2 {\r\n      flex: 1;\r\n\r\n      .center {\r\n        margin-top: -24px;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: 400;\r\n        font-size: 17px;\r\n        color: #00ffb6;\r\n        text-align: center;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .btn {\r\n        width: 133px;\r\n        height: 31px;\r\n        border: 1px solid #2d6cb0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #ffffff;\r\n        border-radius: 30px;\r\n        margin-left: 7%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ltitle {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle1 {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle11 {\r\n    width: 90%;\r\n    margin-top: 80px;\r\n  }\r\n\r\n  .ltitle211 {\r\n    // width: 90%;\r\n    margin-top: 80px;\r\n  }\r\n\r\n  .line1 {\r\n    width: 2px;\r\n    height: 823px;\r\n    opacity: 0.64;\r\n    background-color: #204964;\r\n  }\r\n\r\n  .all1 {\r\n    flex: 1;\r\n\r\n    .nenghao {\r\n      width: 227px;\r\n      height: 173px;\r\n      background: url(\"../../assets/image/nenghao.png\");\r\n      background-size: 100% 100%;\r\n      margin-left: 120px;\r\n      margin-top: 21px;\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      line-height: 213px;\r\n    }\r\n\r\n    .nhp {\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 500;\r\n      font-size: 30px;\r\n      color: #2cc1ff;\r\n      margin-top: 8px;\r\n    }\r\n\r\n    .nh {\r\n      margin-left: 24px;\r\n      margin-top: 5px;\r\n      width: 423px;\r\n      height: 93px;\r\n      border: 1px solid #364d5a;\r\n      background-size: 100% 100%;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      padding-left: 42px;\r\n\r\n      .nhimg {\r\n        width: 96.6px;\r\n        height: 70px;\r\n      }\r\n\r\n      .nhtit {\r\n        width: 148px;\r\n        margin-left: 10px;\r\n        margin-top: 10px;\r\n\r\n        .p11 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #7acfff;\r\n        }\r\n\r\n        .p12 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #ffa170;\r\n        }\r\n\r\n        .p2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .nhtit1 {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-left: 35px;\r\n\r\n        .nhimg1 {\r\n          width: 16px;\r\n          height: 20px;\r\n        }\r\n\r\n        .pp1 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #0df29b;\r\n        }\r\n\r\n        .pp2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffa170;\r\n        }\r\n      }\r\n\r\n      .nht {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .pp {\r\n          margin-left: 35px;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n\r\n          color: #cccccc;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all2 {\r\n    margin-left: 38px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .shinei {\r\n      .itemshei {\r\n        display: flex;\r\n        justify-content: space-around;\r\n\r\n        .nenghaos {\r\n          width: 227px;\r\n          height: 173px;\r\n          background: url(\"../../assets/image/nenghao.png\");\r\n          background-size: 100% 100%;\r\n          text-align: center;\r\n          margin-left: 10px;\r\n          margin-top: 23px;\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #ffffff;\r\n          line-height: 144px;\r\n        }\r\n\r\n        .nhps {\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 500;\r\n          font-size: 21px;\r\n          color: #2cc1ff;\r\n          margin-top: 8px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all3 {\r\n    flex: 668;\r\n    margin-left: 45px;\r\n  }\r\n}\r\n\r\n.shinei {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shuantitle {\r\n  width: 100%;\r\n  display: flex;\r\n  margin-top: 10px;\r\n\r\n  .title {\r\n    width: 95%;\r\n    background: url(\"../../assets/image/title.png\");\r\n    background-size: 100% 100%;\r\n\r\n    height: 25px;\r\n    font-family: Source Han Sans SC;\r\n    font-weight: 400;\r\n    font-size: 25px;\r\n    color: #ffffff;\r\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\r\n    font-style: italic;\r\n    text-align: left;\r\n    line-height: 4px;\r\n    padding-left: 33px;\r\n  }\r\n}\r\n\r\n.nenghao {\r\n  width: 167px;\r\n  height: 113px;\r\n  background: url(\"../../assets/image/nenghao.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin-left: 83px;\r\n  margin-top: 63px;\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #ffffff;\r\n  line-height: 144px;\r\n}\r\n\r\n.nhp {\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 500;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n  margin-top: 8px;\r\n}\r\n\r\n.contents {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: url(\"../../assets/image/zichanbeijin.png\");\r\n  background-size: 100% 100%;\r\n  width: 1863px;\r\n  height: 868px;\r\n  z-index: 99999;\r\n  padding-left: 34px;\r\n  padding-right: 22px;\r\n  padding-top: 21px;\r\n}\r\n\r\n.toubu {\r\n  width: 100%;\r\n\r\n  position: relative;\r\n}\r\n\r\n.el-select {\r\n  margin-top: -1px;\r\n  margin-left: 10px;\r\n  background: #00203d;\r\n  border-radius: 3px;\r\n  border: 1px solid #3e89db;\r\n\r\n  /deep/.el-select__wrapper {\r\n    background: #00203d !important;\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper .is-hovering:not {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper:hover {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__placeholder.is-transparent {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select__placeholder {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select-dropdown__item.is-hovering {\r\n    background-color: #2cc1ff !important;\r\n  }\r\n}\r\n\r\n.sp {\r\n  margin-top: -5px;\r\n  margin-left: 12px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: bold;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n}\r\n\r\n.img1sss {\r\n  cursor: pointer;\r\n  width: 15px;\r\n  height: 15px;\r\n}\r\n</style>"]}]}