<template>
  <keep-alive>
    <div class="contents" v-if="isshow" v-loading="false" element-loading-text="Loading..." :element-loading-spinner="svg"
      element-loading-background="rgba(0, 0, 0, 1)">
      <div class="toubu">
        <div style="margin-left: 20px; display: flex; align-items: center" v-if="false">
          <div style="display: flex; width: 100%; align-items: center">
            <span class="sp">当前位置：</span>
            <el-select class="el-select" v-model="selectvalue1" placeholder="selectvalue1"
              style="width: 64px; height: 35.1px" @change="handleChange">
              <el-option v-for="item in options1" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select class="el-select" v-model="selectvalue2" placeholder="selectvalue2"
              style="width: 64px; height: 35.1px" @change="handleChange">
              <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select class="el-select" v-model="selectvalue3" placeholder="selectvalue3"
              style="width: 78px; height: 35.1px" @change="handleChange">
              <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </div>
          <img v-if="isshow" class="img1sss" @click="anniu()" src="../../assets/image/table-x.png" alt="" />
        </div>

        <div class="all">
          <div class="all1">

            <Titles class="ltitle1" tit="仪器实时状态">
              <div class="dayi">
                <span>共</span>
                <span>{{ sbnum }}</span>
                <span>台仪器安装客户端</span>
              </div>
              <Electricity1 v-if="yiqiStatus" class="zhuzhuangtu" :chartData="yiqiStatus"></Electricity1>
              <!-- <div class="shinei">
              <Electricity4></Electricity4>
            </div> -->
            </Titles>
            <Titles class="ltitle11" tit="人员分布统计">

              <!-- <zhuzhuangtu class="zhuzhuangtu" :chartData="chartData1"></zhuzhuangtu> -->
              <huanxing v-if="userDistribution" :chartData="userDistribution"></huanxing>

            </Titles>
          </div>
          <div class="line1"></div>
          <div class="all2">
            <!-- <Titles class="ltitle1" tit="仪器设备使用情况">
            <div class="shinei">
              <Electricity8></Electricity8>
            </div>
          </Titles>
          <Titles class="ltitle" tit="办公设备使用情况">
            <div class="shinei">
              <Electricity8></Electricity8>
            </div>
          </Titles> -->
            <div>
              <Titles class="ltitle1" tit="课题测试统计">
                <div class="shinei">
                  <Electricity3 v-if="testStatistics" :chartData="testStatistics"></Electricity3>
                </div>
              </Titles>
            </div>
          </div>
          <div class="all3">

            <Titles class="ltitle1" tit="仪器使用排行">
              <div class="shinei">
                <!-- <Electricity6></Electricity6> -->
                <zhuzhuangtu v-if="equipmentRank" class="zhuzhuangtu1" :chartData="equipmentRank"></zhuzhuangtu>
              </div>
            </Titles>
            <Titles class="ltitle1" tit="课题组使用统计">
              <div class="shinei">
                <!-- <huanxing :chartData="chartData"></huanxing> -->
                <zhuzhuangtu1 v-if="topUsers" class="zhuzhuangtu1" :chartData="topUsers"></zhuzhuangtu1>
              </div>
            </Titles>
            <!-- <div class="shuantitle">
            <div style="width: 50%">
              <div class="title">实时负载率</div>
              <div class="nenghao">实时负载率:</div>
              <p class="nhp">30%</p>
            </div>
            <div style="width: 50%">
              <div class="title">实时总功率</div>
              <div class="nenghao">实时总功率:</div>
              <p class="nhp">200Kw</p>
            </div>
          </div>
      -->



          </div>
        </div>
      </div>
    </div>
  </keep-alive>
</template> 

<script>
import Electricity from "@/components/echarts/dianbiao/biao1.vue";
import biao1s from "@/components/echarts/dianbiao/biao1s.vue";
import biao1ss from "@/components/echarts/dianbiao/biao1ss.vue";
import Titles from "@/components/common/Titles.vue";
import Electricity1 from "@/components/dayi/Electricity1.vue";
import Electricity2 from "@/components/echarts/dianbiao/Electricity2.vue";
import Electricity3 from "@/components/dayi//zhexiantu.vue";
import Electricity4 from "@/components/echarts/dianbiao/Electricity4.vue";
import Electricity5 from "@/components/echarts/dianbiao/Electricity5.vue";
import Electricity6 from "@/components/dayi/Electricity6.vue";
import Electricity7 from "@/components/echarts/dianbiao/Electricity7.vue";
import Electricity8 from "@/components/dayi/Electricity8.vue";
import huanxing from "@/components/dayi/xiaobingtu.vue";
import zhuzhuangtu from "@/components/dayi/zhuzhuangtu.vue";
import zhuzhuangtu1 from "@/components/dayi/zhuzhuangtu1.vue";
import axios from "axios";
// 使用环境变量设置基础 API 地址
const baseURL = process.env.VUE_APP_BASE_API || '/lims/api';

const api = axios.create({
  baseURL
});
const headers = {
  clientid: '5a298e93-158d-4e22-83cf-6ceb62e9b4f1',
  clientsecret: '2c8ec39e-9887-482a-b28b-e64c496b601c'
};
export default {
  components: {
    Titles,
    Electricity1,
    Electricity,
    Electricity2,
    Electricity3,
    Electricity4,
    Electricity5,
    Electricity6,
    Electricity7,
    Electricity8,
    huanxing,
    biao1s,
    biao1ss,
    zhuzhuangtu,
    zhuzhuangtu1
  },
  data() {
    return {
      loading: true,
      loading1: true,
      loading2: true,
      loading3: true,
      loading4: true,
      loading5: true,
      //svg: 'el-icon-loading' ,// 或者自定义 SVG 图标
      sbnum: 723,
      chartDatazz: {
        title: ["已领用", "未领用"],
        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],
        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],
        yAxisdata: ["一月", "二月", "三月", "四月", "五月", "六月", "七月"],
      },
      chartDatazz1: {
        title: ["已领用", "未领用"],
        xAxisdata1: [80, 80, 97, 53, 95, 70, 88],
        xAxisdata2: [100, 100, 100, 100, 100, 100, 100],
        yAxisdata: ["一月", "二月", "三月", "四月", "五月", "六月", "七月"],
      },
      chartData: {
        value: [1321, 18582, 651],
        legend: [
          "校外人员",
          "校内人员",
          "管理员",
        ],
      },
      chartData1: {
        title: ["已领用", "未领用"],
        xAxisdata: ["20日", "21日", "22日", "23日", "25日", "24日",],
        yAxisdata1: [4, 7, 5, 9, 6, 5],
        yAxisdata2: [4, 7, 5, 9, 6, 5],
      },
      chartData2: [
        {
          name: "正在使用",
          value: 271,
        },
        {
          name: "待机中",
          value: 452,

        },
        {
          name: "故障",
          value: 21,

        }],
      chartData3: [
        {
          name: "正在使用",
          value: 271,
        },
        {
          name: "待机中",
          value: 452,

        },
        {
          name: "故障",
          value: 21,

        }],
      isshow: true,
      options: [
        {
          value: "总览",
          label: "总览",
        },
        {
          value: "能耗分析",
          label: "能耗分析",
        },
        {
          value: "能流分析",
          label: "能流分析",
        },
        {
          value: "设备状态",
          label: "设备状态",
        },
        {
          value: "一键抄表",
          label: "一键抄表",
        },
        {
          value: "费用管理",
          label: "费用管理",
        },
        {
          value: "碳排放管理",
          label: "碳排放管理",
        },
      ],
      selectvalue2: "B3",
      options2: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B3",
      options3: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue1: "B3",
      options1: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B1栋",
      options4: [
        {
          value: "B1栋",
          label: "B1栋",
        },
        {
          value: "B2栋",
          label: "B2栋",
        },
        {
          value: "B3栋",
          label: "B3栋",
        },
        {
          value: "B4栋",
          label: "B4栋",
        },
        {
          value: "W1栋",
          label: "W1栋",
        },
        {
          value: "W2栋",
          label: "W2栋",
        },
      ],
      selectvalue4: "B1栋",
      optionData: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
      optionData1: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
    };
  },
  computed: {
    loading() {
      return this.loading1 || this.loading2 || this.loading3 || this.loading4 || this.loading5;
    },
    // 使用 Vuex 的 getters 获取持久化的数据
    equipmentRank() {
      return this.$store.getters["equipment/equipmentRank"];
    },
    yiqiStatus() {
      return this.$store.getters["equipment/yiqiStatus"];
    },
    userDistribution() {
      return this.$store.getters["equipment/userDistribution"];
    },
    testStatistics() {
      return this.$store.getters["equipment/testStatistics"];
    },
    topUsers() {
      return this.$store.getters["equipment/topUsers"];
    },
  },
  mounted() {

    // this.$store.dispatch('equipment/fetchEquipmentRank');
    // this.$store.dispatch('equipment/getdata2');
    // this.$store.dispatch('equipment/getdata3');
    // this.$store.dispatch('equipment/getdata4');
    // this.$store.dispatch('equipment/getdata5');

    if (!this.equipmentRank.length) {
      this.$store.dispatch('equipment/fetchEquipmentRank'); // 如果没有缓存，获取数据
    }
    if (!this.yiqiStatus.length) {
      this.$store.dispatch('equipment/getdata2'); // 如果没有缓存，获取数据
    }
    if (!this.userDistribution.length) {
      this.$store.dispatch('equipment/getdata3'); // 如果没有缓存，获取数据
    }
    if (!this.testStatistics.length) {
      this.$store.dispatch('equipment/getdata4'); // 如果没有缓存，获取数据
    }
    if (!this.topUsers.length) {
      this.$store.dispatch('equipment/getdata5'); // 如果没有缓存，获取数据
    }
    setInterval(() => {
      this.$store.dispatch('equipment/fetchEquipmentRank');
      this.$store.dispatch('equipment/getdata2');
      this.$store.dispatch('equipment/getdata3');
      this.$store.dispatch('equipment/getdata4');
      this.$store.dispatch('equipment/getdata5');
    }, 36000000);
    // this.getdata1()
    this.getdata2()
    // this.getdata3()
    // this.getdata4()
    // this.getdata5()
    // setInterval(() => {
    //   this.getdata1()
    //   this.getdata2()
    //   this.getdata3()
    //   this.getdata4()
    //   this.getdata5()
    // }, 10000);

  },

  methods: {

    async getdata1() {  //仪器使用排行
      try {
        const response = await api.post('', {

          "method": "equipment/time_rank",
          "params": {
            "num": 10,
            "start": 1704038400,
            "end": 1735660800
          }

        }, { headers });
        // 检查是否成功拿到 token
        if (response.data) {
          console.log('仪器使用排行:', response.data);
          let data = response.data.response
          // 提取 names 和 times
          const names = data.map(item => item.name).reverse();
          const times = data.map(item => item.time).reverse();
          this.chartDatazz1.yAxisdata = names
          this.chartDatazz1.xAxisdata1 = times
          this.loading1 = false
        }
      } catch (error) {
        console.error('登录失败:', error);
      }
    },

    async getdata2() {  //仪器使用情况
      try {
        const response = await api.post('', {
          "method": "equipment/getSummaryInfo",
          "params": {}
        }, { headers });
        // 检查是否成功拿到 token
        if (response.data) {
          this.sbnum = response.data.response.controlCount,
            console.log('仪器使用情况:', response.data.response);


        }

      } catch (error) {
        console.error('登录失败:', error);
      }
    },
    async getdata3() {  //人员分布情况
      try {
        const response = await api.post('', {
          "method": "summarize/userStatus",
          "params": {}
        }, { headers });
        // 检查是否成功拿到 token
        if (response.data) {
          console.log('人员分布情况:', response.data);
          this.chartData = {
            value: [response.data.response.outer, response.data.response.inner, response.data.response.incharge],
            legend: [
              "校外人员",
              "校内人员",
              "管理员",
            ],
          }
          this.loading3 = false
        }
      } catch (error) {
        console.error('登录失败:', error);
      }
    },
    async getdata4() {  //课题测试情况

      try {
        const response = await api.post('', {
          "method": "summarize/labStatus",
          "params": {}
        }, { headers });
        // 检查是否成功拿到 token
        if (response) {
          console.log('课题测试情况:', response.data.response);
          this.chartData3 = [
            {
              name: "总课题数",
              value: response.data.response.project,
            },
            {
              name: "课题数",
              value: response.data.response.lab,

            },
            {
              name: "测试数",
              value: response.data.response.test,

            }]
          this.loading4 = false
        }
      } catch (error) {
        console.error('登录失败:', error);
      }
    },
    async getdata5() {  //用户排行
      try {
        const response = await api.post('', {
          "method": "eq_reserv/getTopUsers",
          "params": {
            "num": 9,
            "year": 2024
          }
        }, { headers });
        // 检查是否成功拿到 token
        if (response.data) {
          console.log('用户排行:', response.data.response);
          let data = response.data.response
          // 提取 names 和 times
          const names = data.map(item => item.name).reverse();
          const times = data.map(item => item.time).reverse();
          this.chartDatazz.yAxisdata = names
          this.chartDatazz.xAxisdata1 = times
        }
        this.loading5 = false
      } catch (error) {
        console.error('登录失败:', error);
      }
    },
    anniu() {
      this.isshow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.zhuzhuangtu {
  margin-top: 20px;
  margin-bottom: 10px;
}

.zhuzhuangtu1 {
  margin-top: -26px;

}

.all {
  display: flex;
  flex-direction: row;
  margin-top: 5px;

  .zong {
    display: flex;
    flex-direction: row;
    margin-top: 10px;

    .echart1,
    .echart2 {
      flex: 1;

      .center {
        margin-top: -24px;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: 400;
        font-size: 17px;
        color: #00ffb6;
        text-align: center;
        margin-bottom: 10px;
      }

      .btn {
        width: 133px;
        height: 31px;
        border: 1px solid #2d6cb0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        border-radius: 30px;
        margin-left: 7%;
      }
    }
  }

  .ltitle1 {
    margin-top: 10px;
  }

  .ltitle11 {
    margin-top: 30px;
    position: relative;
  }

  .dayi {
    position: absolute;
    top: 42px;
    left: 68px;
    z-index: 20;
    font-size: 22px;
    color: #fff;
    text-align: center;

    span:nth-child(2) {
      font-size: 32px;
    }
  }

  .line1 {
    width: 2px;
    height: 823px;
    opacity: 0.64;
    background-color: #204964;
  }

  .all1 {
    flex: 462;

    .nenghao {
      width: 227px;
      height: 173px;
      background: url("../../assets/image/nenghao.png");
      background-size: 100% 100%;
      margin-left: 120px;
      margin-top: 21px;
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      line-height: 213px;
    }

    .nhp {
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 30px;
      color: #2cc1ff;
      margin-top: 8px;
    }

    .nh {
      margin-left: 24px;
      margin-top: 5px;
      width: 423px;
      height: 93px;
      border: 1px solid #364d5a;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 42px;

      .nhimg {
        width: 96.6px;
        height: 70px;
      }

      .nhtit {
        width: 148px;
        margin-left: 10px;
        margin-top: 10px;

        .p11 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #7acfff;
        }

        .p12 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #ffa170;
        }

        .p2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffffff;
        }
      }

      .nhtit1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-left: 35px;

        .nhimg1 {
          width: 16px;
          height: 20px;
        }

        .pp1 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #0df29b;
        }

        .pp2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffa170;
        }
      }

      .nht {
        margin-top: 10px;
        display: flex;
        flex-direction: column;

        .pp {
          margin-left: 35px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;

          color: #cccccc;
        }
      }
    }
  }

  .all2 {
    margin-left: 38px;
    flex: 667;
    display: flex;
    flex-direction: column;

    .shinei {
      .itemshei {
        display: flex;
        justify-content: space-around;

        .nenghaos {
          width: 227px;
          height: 173px;
          background: url("../../assets/image/nenghao.png");
          background-size: 100% 100%;
          text-align: center;
          margin-left: 10px;
          margin-top: 23px;
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 144px;
        }

        .nhps {
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          font-size: 21px;
          color: #2cc1ff;
          margin-top: 8px;
        }
      }
    }
  }

  .all3 {
    flex: 668;
    margin-left: 45px;
  }
}

.shinei {
  width: 100%;
  height: 100%;
}

.shuantitle {
  width: 100%;
  display: flex;
  margin-top: 10px;

  .title {
    width: 95%;
    background: url("../../assets/image/title.png");
    background-size: 100% 100%;

    height: 25px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 25px;
    color: #ffffff;
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);
    font-style: italic;
    text-align: left;
    line-height: 4px;
    padding-left: 33px;
  }
}

.nenghao {
  width: 167px;
  height: 113px;
  background: url("../../assets/image/nenghao.png");
  background-size: 100% 100%;
  text-align: center;
  margin-left: 83px;
  margin-top: 63px;
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 144px;
}

.nhp {
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 21px;
  color: #2cc1ff;
  margin-top: 8px;
}

.contents {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../../assets/image/zichanbeijin.png");
  background-size: 100% 100%;
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;
}

.toubu {
  width: 100%;

  position: relative;
}

.el-select {
  margin-top: -1px;
  margin-left: 10px;
  background: #00203d;
  border-radius: 3px;
  border: 1px solid #3e89db;

  /deep/.el-select__wrapper {
    background: #00203d !important;
    box-shadow: none;
  }

  /deep/.el-select__wrapper .is-hovering:not {
    box-shadow: none;
  }

  /deep/.el-select__wrapper:hover {
    box-shadow: none;
  }

  /deep/.el-select__placeholder.is-transparent {
    color: #2cc1ff;
  }

  /deep/.el-select__placeholder {
    color: #2cc1ff;
  }

  /deep/.el-select-dropdown__item.is-hovering {
    background-color: #2cc1ff !important;
  }
}

.sp {
  margin-top: -5px;
  margin-left: 12px;
  font-family: Alibaba PuHuiTi;
  font-weight: bold;
  font-size: 21px;
  color: #2cc1ff;
}

.img1sss {
  cursor: pointer;
  width: 15px;
  height: 15px;
}
</style>