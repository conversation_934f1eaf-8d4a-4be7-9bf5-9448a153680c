<template>
  <div class="tt" :style="{ height: computedHeight }">
    <div class="title">
      <div>{{ tit }}</div>
      <!-- <div v-if="isshow" class="anniu" @click="open()">查看详情</div> -->
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: ["tit", "isshow"],
  computed: {
    // 动态计算高度
    computedHeight() {
      return this.tit === "异常跟踪处理" ? "330px" : "898px";
    },
  },
  methods: {
    open() {
      if (this.tit == "疾控中心介绍") {
        this.$emit("open-dialog", 0);
      } else if (this.tit == "疾控中心风采") {
        this.$emit("open-dialog", 1);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.tt {
  background: url("../../assets/image/titlebg1s.png");
  background-size: 100% 100%;
  width: 387px;
  padding-top: 20px;
  padding-left: 20px;
}

.title {
  letter-spacing: 0.5px;
  background: url("../../assets/image/title1.png");
  background-size: 100% 100%;
  width: 354px;
  height: 34px;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 21px;
  color: #ffffff;
  text-align: left;
  font-style: italic;
  line-height: 9px;
  padding-right: 35px;
  display: flex;
  justify-content: flex-end;

  .anniu {
    background: url("../../assets/image/biaoqian.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 12px;
    color: #ffffff;
    width: 78px;
    height: 35px;
    text-align: center;
    line-height: 35px;
    cursor: pointer;
  }
}
</style>
