<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        title: {
          text: "Kwh",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },

        tooltip: {
          show: false,
        },
        grid: {
          top: "18%",
          bottom: "1%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 14,
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#939ab6",
                opacity: 0.15,
              },
            },
            data: [
              "3/24",
              "3/25",
              "3/26",
              "3/27",
              "3/28",
              "3/29",
              "3/30",
              "3/31",
              "4/01",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",

            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 14,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "2",
            type: "line",
            z: 3,
            showSymbol: false,
            smooth: true, // 这里设置为true，让线条平滑
            lineStyle: {
              width: 1,
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(59,102,246)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(118,237,252)", // 100% 处的颜色
                  },
                ],
              },
              shadowBlur: 4,
              shadowColor: "rgba(69,126,247,.2)",
              shadowOffsetY: 4,
            },
            areaStyle: {
              normal: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#05244A", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#0F4C77", // 100% 处的颜色
                    },
                  ],
                },
              },
            },
            data: [
              80, 80, 80, 80, 250, 350, 450, 450, 450, 450, 250, 200, 100, 60,
            ],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 700px;
  height: 380px;
}

@media (max-height: 1080px) {
  .echart {
    width: 700px;
    height: 380px !important;
  }
}
</style>