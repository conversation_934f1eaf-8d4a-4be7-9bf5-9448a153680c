{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\tongji\\baojing.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\tongji\\baojing.vue", "mtime": 1750744791852}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["huanxing2", "huanxing1", "Electricity", "biao1s", "biao1ss", "Titles", "echarts1", "Electricity2", "Electricity3", "Electricity4", "Electricity5", "Electricity6", "Electricity7", "Electricity8", "huanxing", "zhuzhuangtu", "echarts3", "getDeviceData", "getDevicedetails", "getDeviceWarningList", "components", "data", "warningStats", "chartData", "value", "legend", "echartData2", "unit", "xdata", "ydata", "name", "type", "symbol", "smooth", "echartData3", "chartDataxf", "chartData1", "title", "xAxisdata", "yAxisdata1", "yAxisdata2", "chartData2", "chartData3", "isshow", "options", "label", "selectvalue2", "options2", "selectvalue3", "options3", "selectvalue1", "options1", "options4", "selectvalue4", "optionData", "itemStyle", "color", "opacity", "optionData1", "gasWarningData", "mounted", "fetchWarningStats", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setInterval", "methods", "res", "pageSize", "currentPage", "hasFixed", "code", "rows", "allWarningTypes", "压力报警", "氧气浓度报警", "温度报警", "湿度报警", "stats", "Object", "keys", "for<PERSON>ach", "total", "unresolved", "item", "warningCategory", "status", "entries", "map", "category", "count", "console", "log", "error", "deviceTypes", "deviceType", "gasTypes", "deviceName", "gasType", "includes", "electricity4Data", "displayNames", "anniu"], "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\tongji\\baojing.vue"], "sourcesContent": ["<template>\r\n  <div class=\"contents\" v-if=\"isshow\">\r\n    <div class=\"toubu\">\r\n      <div style=\"margin-left: 20px; display: flex; align-items: center\" v-if=\"false\">\r\n        <div style=\"display: flex; width: 100%; align-items: center\">\r\n          <span class=\"sp\">当前位置：</span>\r\n          <el-select class=\"el-select\" v-model=\"selectvalue1\" placeholder=\"selectvalue1\"\r\n            style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n            <el-option v-for=\"item in options1\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n          <el-select class=\"el-select\" v-model=\"selectvalue2\" placeholder=\"selectvalue2\"\r\n            style=\"width: 64px; height: 35.1px\" @change=\"handleChange\">\r\n            <el-option v-for=\"item in options2\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n          <el-select class=\"el-select\" v-model=\"selectvalue3\" placeholder=\"selectvalue3\"\r\n            style=\"width: 78px; height: 35.1px\" @change=\"handleChange\">\r\n            <el-option v-for=\"item in options3\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n          </el-select>\r\n        </div>\r\n        <img v-if=\"isshow\" class=\"img1sss\" @click=\"anniu()\" src=\"../../assets/image/table-x.png\" alt=\"\" />\r\n      </div>\r\n\r\n      <div class=\"all\">\r\n        <div class=\"all1\">\r\n          <Titles class=\"ltitle\" tit=\"环境报警统计\">\r\n\r\n            <div class=\"boxxx\" style=\"margin-top:30px;\">\r\n\r\n              <huanxing :warningData=\"warningStats\"></huanxing>\r\n            </div>\r\n\r\n          </Titles>\r\n          <Titles class=\"ltitle11\" tit=\"当月气体报警统计\">\r\n            <Electricity4 :gasWarningData=\"gasWarningData\"></Electricity4>\r\n            <!-- <div class=\"shinei\">\r\n              <Electricity4></Electricity4>\r\n            </div> -->\r\n          </Titles>\r\n        </div>\r\n        <!-- <div class=\"line1\"></div> -->\r\n        <div class=\"all2\">\r\n          <Titles class=\"ltitle1\" tit=\"传感器实时报警占比\">\r\n            <div class=\"shinei\">\r\n              <huanxing2 style=\"margin-top:30px;margin-bottom:26px\" :chartData=\"chartData\"></huanxing2>\r\n            </div>\r\n          </Titles>\r\n          <Titles class=\"ltitle211\" tit=\"新风设备报警占比\">\r\n            <div class=\"shinei\">\r\n              <huanxing2 style=\"margin-top:30px\" :chartData=\"chartDataxf\"></huanxing2>\r\n            </div>\r\n          </Titles>\r\n          <!-- <div>\r\n            <Titles class=\"ltitle1\" tit=\"设备购入时间\">\r\n              <div class=\"shinei\">\r\n                <Electricity3 :chartData=\"chartData3\"></Electricity3>\r\n              </div>\r\n            </Titles>\r\n          </div> -->\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport huanxing2 from \"@/components/fuyong/xiaobingtu.vue\";\r\nimport huanxing1 from \"@/components/fuyong/indexhuan.vue\";\r\nimport Electricity from \"@/components/echarts/dianbiao/biao1.vue\";\r\nimport biao1s from \"@/components/echarts/dianbiao/biao1s.vue\";\r\nimport biao1ss from \"@/components/echarts/dianbiao/biao1ss.vue\";\r\nimport Titles from \"@/components/common/Titles.vue\";\r\nimport echarts1 from \"@/components/echarts/bingjifang/echarts1.vue\";\r\nimport Electricity2 from \"@/components/echarts/dianbiao/Electricity2.vue\";\r\nimport Electricity3 from \"@/components/fuyong//zhexiantu.vue\";\r\nimport Electricity4 from \"@/components/gongqi/Electricity4.vue\";\r\nimport Electricity5 from \"@/components/echarts/dianbiao/Electricity5.vue\";\r\nimport Electricity6 from \"@/components/fuyong/Electricity6.vue\";\r\nimport Electricity7 from \"@/components/fuyong/Electricity7.vue\";\r\n// import Electricity7 from \"@/components/echarts/dianbiao/Electricity7.vue\";\r\nimport Electricity8 from \"@/components/fuyong/Electricity8.vue\";\r\nimport huanxing from \"@/components/echarts/huanxing.vue\";\r\nimport zhuzhuangtu from \"@/components/fuyong/zhuzhuangtu.vue\";\r\nimport echarts3 from \"@/components/echarts/kongya/echarts2.vue\";\r\nimport {\r\n  getDeviceData,\r\n  getDevicedetails,\r\n  getDeviceWarningList,\r\n} from \"@/api/device.js\";\r\nexport default {\r\n  components: {\r\n\r\n    echarts1,\r\n    echarts3,\r\n    huanxing2,\r\n    Titles,\r\n    Electricity,\r\n    Electricity2,\r\n    Electricity3,\r\n    Electricity4,\r\n    Electricity5,\r\n    Electricity6,\r\n    Electricity7,\r\n    Electricity8,\r\n    huanxing,\r\n    huanxing1,\r\n    biao1s,\r\n    biao1ss,\r\n    zhuzhuangtu\r\n  },\r\n  data() {\r\n    return {\r\n      warningStats: [],\r\n      chartData: {\r\n        value: [1, 1, 1, 1],\r\n        legend: [\r\n          \"温度传感器\",\r\n          \"湿度传感器\",\r\n          \"压差传感器\",\r\n          \"氧气传感器\",\r\n        ],\r\n      },\r\n      echartData2: {\r\n        unit: \"单位:℃\",\r\n        legend: [\"今日\", \"昨日\"],\r\n        xdata: [\r\n          \"1:00\",\r\n          \"3:00\",\r\n          \"5:00\",\r\n          \"7:00\",\r\n          \"9:00\",\r\n          \"11:00\",\r\n          \"13:00\",\r\n          \"15:00\",\r\n          \"17:00\",\r\n          \"19:00\",\r\n          \"21:00\",\r\n          \"23:00\",\r\n        ],\r\n        ydata: [\r\n          {\r\n            name: \"今日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [23, 22, 24, 23, 22, 21, 23, 23, 23, 24, 22, 23],\r\n          },\r\n          {\r\n            name: \"昨日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [21, 22, 22, 23, 21, 22, 22, 21, 23, 21, 23, 23],\r\n          },\r\n        ],\r\n      },\r\n      echartData3: {\r\n        unit: \"单位:%RH\",\r\n        legend: [\"今日\", \"昨日\"],\r\n        xdata: [\r\n          \"1:00\",\r\n          \"3:00\",\r\n          \"5:00\",\r\n          \"7:00\",\r\n          \"9:00\",\r\n          \"11:00\",\r\n          \"13:00\",\r\n          \"15:00\",\r\n          \"17:00\",\r\n          \"19:00\",\r\n          \"21:00\",\r\n          \"23:00\",\r\n        ],\r\n        ydata: [\r\n          {\r\n            name: \"今日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [43, 42, 44, 43, 45, 46, 42, 45, 42, 41.5, 42, 43],\r\n          },\r\n          {\r\n            name: \"昨日\",\r\n            type: \"line\",\r\n            symbol: \"none\",\r\n            smooth: true,\r\n            data: [41, 42, 44, 44, 49, 47, 41, 44, 41, 43, 43, 46]\r\n\r\n          },\r\n        ],\r\n      },\r\n      chartDataxf: {\r\n        value: [2, 3, 2],\r\n        legend: [\r\n          \"空调\",\r\n          \"水泵\",\r\n          \"冷却塔\",\r\n        ],\r\n      },\r\n      chartData1: {\r\n        title: [\"已领用\", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [720, 710, 730, 705, 715, 725],\r\n        yAxisdata2: [480, 490, 470, 495, 485, 475]\r\n      },\r\n      chartData2: {\r\n        title: [\" \", \"未领用\"],\r\n        xAxisdata: [\"20日\", \"21日\", \"22日\", \"23日\", \"25日\", \"24日\",],\r\n        yAxisdata1: [600, 580, 620, 590, 610, 605],\r\n        yAxisdata2: [300, 320, 280, 310, 290, 295]\r\n      },\r\n      chartData3: {\r\n        title: [\"仪器设备\", \"办公设备\"],\r\n        xAxisdata: [\r\n          \"10/16\",\r\n          \"10/17\",\r\n          \"10/18\",\r\n          \"10/19\",\r\n          \"10/20\",\r\n          \"10/21\",\r\n          \"10/22\",\r\n          \"10/23\",\r\n          \"10/24\",\r\n          \"10/25\",\r\n          \"10/26\",\r\n          \"10/27\",\r\n          \"10/28\",\r\n\r\n        ],\r\n        yAxisdata1: [4, 17, 5, 9, 6, 5, 0, 0, 12, 0, 4, 0, 1],\r\n        yAxisdata2: [14, 7, 2, 3, 16, 5, 0, 0, 2, 0, 13, 0, 0],\r\n      },\r\n      isshow: true,\r\n      options: [\r\n        {\r\n          value: \"总览\",\r\n          label: \"总览\",\r\n        },\r\n        {\r\n          value: \"能耗分析\",\r\n          label: \"能耗分析\",\r\n        },\r\n        {\r\n          value: \"能流分析\",\r\n          label: \"能流分析\",\r\n        },\r\n        {\r\n          value: \"设备状态\",\r\n          label: \"设备状态\",\r\n        },\r\n        {\r\n          value: \"一键抄表\",\r\n          label: \"一键抄表\",\r\n        },\r\n        {\r\n          value: \"费用管理\",\r\n          label: \"费用管理\",\r\n        },\r\n        {\r\n          value: \"碳排放管理\",\r\n          label: \"碳排放管理\",\r\n        },\r\n      ],\r\n      selectvalue2: \"B3\",\r\n      options2: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B3\",\r\n      options3: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue1: \"B3\",\r\n      options1: [\r\n        {\r\n          value: \"B3\",\r\n          label: \"B3\",\r\n        },\r\n      ],\r\n      selectvalue3: \"B1栋\",\r\n      options4: [\r\n        {\r\n          value: \"B1栋\",\r\n          label: \"B1栋\",\r\n        },\r\n        {\r\n          value: \"B2栋\",\r\n          label: \"B2栋\",\r\n        },\r\n        {\r\n          value: \"B3栋\",\r\n          label: \"B3栋\",\r\n        },\r\n        {\r\n          value: \"B4栋\",\r\n          label: \"B4栋\",\r\n        },\r\n        {\r\n          value: \"W1栋\",\r\n          label: \"W1栋\",\r\n        },\r\n        {\r\n          value: \"W2栋\",\r\n          label: \"W2栋\",\r\n        },\r\n      ],\r\n      selectvalue4: \"B1栋\",\r\n      optionData: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      optionData1: [\r\n        {\r\n          name: \"一级告警\",\r\n          value: 16,\r\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"二级告警\",\r\n          value: 27,\r\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"三级告警\",\r\n          value: 17,\r\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\r\n        },\r\n        {\r\n          name: \"四级告警\",\r\n          value: 40,\r\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\r\n        },\r\n      ],\r\n      // 气体报警统计数据，用于传递给Electricity4组件\r\n      gasWarningData: []\r\n    };\r\n  },\r\n  mounted() {\r\n    this.fetchWarningStats();\r\n    this.fetchgqwarn()\r\n    // 每30秒更新一次数据\r\n    setInterval(() => {\r\n      this.fetchWarningStats();\r\n    }, 30000);\r\n  },\r\n  methods: {\r\n    async fetchWarningStats() {\r\n      try {\r\n        const res = await getDeviceWarningList({\r\n          pageSize: 9999,\r\n          currentPage: 1,\r\n          hasFixed: \"N\",\r\n        });\r\n\r\n        if (res.code === 200 && res.rows) {\r\n          // 定义所有可能的报警类型及其阈值\r\n          const allWarningTypes = {\r\n            压力报警: 14,\r\n            氧气浓度报警: 48,\r\n            温度报警: 67,\r\n            湿度报警: 67,\r\n            // '气体泄漏报警': 50\r\n          };\r\n\r\n          // 统计各类型报警数量\r\n          const stats = {};\r\n          // 初始化所有报警类型的计数为0\r\n          Object.keys(allWarningTypes).forEach((type) => {\r\n            stats[type] = {\r\n              total: 0,\r\n              unresolved: 0,\r\n            };\r\n          });\r\n\r\n          // 统计实际数据\r\n          res.rows.forEach((item) => {\r\n            if (stats[item.warningCategory]) {\r\n              stats[item.warningCategory].total++;\r\n              if (item.status === \"N\") {\r\n                stats[item.warningCategory].unresolved++;\r\n              }\r\n            }\r\n          });\r\n\r\n          // 转换为图表所需格式\r\n          this.warningStats = Object.entries(stats).map(\r\n            ([category, count]) => ({\r\n              label: `${category}(${count.total}/${allWarningTypes[category]})`,\r\n              value: count.total,\r\n            })\r\n          );\r\n\r\n          console.log(this.warningStats, \"报警统计数据\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取报警统计数据失败:\", error);\r\n      }\r\n    },\r\n    async fetchgqwarn() {\r\n      try {\r\n        const res = await getDeviceWarningList({\r\n          pageSize: 9999,\r\n          currentPage: 1,\r\n          hasFixed: \"N\",\r\n          deviceTypes: 'GQXT',\r\n          deviceType: 'GQXT'\r\n        });\r\n\r\n        if (res.code === 200 && res.rows) {\r\n          console.log(res, \"报警统计数据\");\r\n\r\n          // 定义需要统计的气体类型\r\n          const gasTypes = {\r\n            '一氧化碳': 0,\r\n            '二氧化碳': 0,\r\n            '氢气': 0,\r\n            '氧气': 0,\r\n            '氨气': 0,\r\n            '甲烷': 0\r\n          };\r\n\r\n          // 统计每种气体的报警数量\r\n          res.rows.forEach(item => {\r\n            if (item.deviceName) {\r\n              Object.keys(gasTypes).forEach(gasType => {\r\n                if (item.deviceName.includes(gasType)) {\r\n                  gasTypes[gasType]++;\r\n                }\r\n              });\r\n            }\r\n          });\r\n\r\n          // 转换为Electricity4组件所需的数据格式\r\n          const electricity4Data = Object.entries(gasTypes).map(([gasType, count]) => {\r\n            // 根据气体类型映射到Electricity4组件中的显示名称\r\n            const displayNames = {\r\n              '氧气': '氧气O₂',\r\n              '氢气': '氢气H₂',\r\n              '二氧化碳': '二氧化碳CO₂',\r\n              '一氧化碳': '一氧化碳CO',\r\n              '氨气': '氨气NH₃',\r\n              '甲烷': '甲烷'\r\n            };\r\n\r\n            return {\r\n              name: displayNames[gasType] || gasType,\r\n              value: count\r\n            };\r\n          });\r\n\r\n          console.log('气体报警统计数据:', electricity4Data);\r\n\r\n          // 将数据存储到data中，供Electricity4组件使用\r\n          this.gasWarningData = electricity4Data;\r\n\r\n          return electricity4Data;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取报警统计数据失败:\", error);\r\n      }\r\n    },\r\n    anniu() {\r\n      this.isshow = false;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.zhuzhuangtu {\r\n  margin-top: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.shbei {\r\n  margin-top: 10px;\r\n\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  width: 411px;\r\n  height: 70px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n\r\n  .item {\r\n    display: flex;\r\n\r\n    img {\r\n      width: 58px;\r\n      height: 56px;\r\n    }\r\n\r\n    .numlist {\r\n      margin-left: 2px;\r\n\r\n      .it1 {\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: bold;\r\n        font-size: 25px;\r\n        color: #ffffff;\r\n      }\r\n\r\n      .it2 {\r\n        font-family: Alibaba PuHuiTi;\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #00ffff;\r\n      }\r\n\r\n      .it3 {\r\n        color: #00ffcc;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.boxxx {\r\n  margin-left: 116px;\r\n  margin-bottom: 18px;\r\n  // background: url(\"../assets/image/zuoshang1.png\");\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n\r\n  width: 350px;\r\n  height: 284px;\r\n}\r\n\r\n.all {\r\n  display: flex;\r\n  flex-direction: row;\r\n  margin-top: 5px;\r\n\r\n  .zong {\r\n    display: flex;\r\n    flex-direction: row;\r\n    margin-top: 10px;\r\n\r\n    .echart1,\r\n    .echart2 {\r\n      flex: 1;\r\n\r\n      .center {\r\n        margin-top: -24px;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: 400;\r\n        font-size: 17px;\r\n        color: #00ffb6;\r\n        text-align: center;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .btn {\r\n        width: 133px;\r\n        height: 31px;\r\n        border: 1px solid #2d6cb0;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-family: \"Source Han Sans SC\", sans-serif;\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #ffffff;\r\n        border-radius: 30px;\r\n        margin-left: 7%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .ltitle {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle1 {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .ltitle11 {\r\n    width: 90%;\r\n    margin-top: 80px;\r\n  }\r\n\r\n  .ltitle211 {\r\n    // width: 90%;\r\n    margin-top: 80px;\r\n  }\r\n\r\n  .line1 {\r\n    width: 2px;\r\n    height: 823px;\r\n    opacity: 0.64;\r\n    background-color: #204964;\r\n  }\r\n\r\n  .all1 {\r\n    flex: 1;\r\n\r\n    .nenghao {\r\n      width: 227px;\r\n      height: 173px;\r\n      background: url(\"../../assets/image/nenghao.png\");\r\n      background-size: 100% 100%;\r\n      margin-left: 120px;\r\n      margin-top: 21px;\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      line-height: 213px;\r\n    }\r\n\r\n    .nhp {\r\n      text-align: center;\r\n      font-family: Alibaba PuHuiTi;\r\n      font-weight: 500;\r\n      font-size: 30px;\r\n      color: #2cc1ff;\r\n      margin-top: 8px;\r\n    }\r\n\r\n    .nh {\r\n      margin-left: 24px;\r\n      margin-top: 5px;\r\n      width: 423px;\r\n      height: 93px;\r\n      border: 1px solid #364d5a;\r\n      background-size: 100% 100%;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      padding-left: 42px;\r\n\r\n      .nhimg {\r\n        width: 96.6px;\r\n        height: 70px;\r\n      }\r\n\r\n      .nhtit {\r\n        width: 148px;\r\n        margin-left: 10px;\r\n        margin-top: 10px;\r\n\r\n        .p11 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #7acfff;\r\n        }\r\n\r\n        .p12 {\r\n          font-family: PangMenZhengDao;\r\n          font-weight: 400;\r\n          font-size: 30px;\r\n          color: #ffa170;\r\n        }\r\n\r\n        .p2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n\r\n      .nhtit1 {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-left: 35px;\r\n\r\n        .nhimg1 {\r\n          width: 16px;\r\n          height: 20px;\r\n        }\r\n\r\n        .pp1 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #0df29b;\r\n        }\r\n\r\n        .pp2 {\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n          color: #ffa170;\r\n        }\r\n      }\r\n\r\n      .nht {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        flex-direction: column;\r\n\r\n        .pp {\r\n          margin-left: 35px;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 17px;\r\n\r\n          color: #cccccc;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all2 {\r\n    margin-left: 38px;\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .shinei {\r\n      .itemshei {\r\n        display: flex;\r\n        justify-content: space-around;\r\n\r\n        .nenghaos {\r\n          width: 227px;\r\n          height: 173px;\r\n          background: url(\"../../assets/image/nenghao.png\");\r\n          background-size: 100% 100%;\r\n          text-align: center;\r\n          margin-left: 10px;\r\n          margin-top: 23px;\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n          color: #ffffff;\r\n          line-height: 144px;\r\n        }\r\n\r\n        .nhps {\r\n          text-align: center;\r\n          font-family: Alibaba PuHuiTi;\r\n          font-weight: 500;\r\n          font-size: 21px;\r\n          color: #2cc1ff;\r\n          margin-top: 8px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .all3 {\r\n    flex: 668;\r\n    margin-left: 45px;\r\n  }\r\n}\r\n\r\n.shinei {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.shuantitle {\r\n  width: 100%;\r\n  display: flex;\r\n  margin-top: 10px;\r\n\r\n  .title {\r\n    width: 95%;\r\n    background: url(\"../../assets/image/title.png\");\r\n    background-size: 100% 100%;\r\n\r\n    height: 25px;\r\n    font-family: Source Han Sans SC;\r\n    font-weight: 400;\r\n    font-size: 25px;\r\n    color: #ffffff;\r\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\r\n    font-style: italic;\r\n    text-align: left;\r\n    line-height: 4px;\r\n    padding-left: 33px;\r\n  }\r\n}\r\n\r\n.nenghao {\r\n  width: 167px;\r\n  height: 113px;\r\n  background: url(\"../../assets/image/nenghao.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin-left: 83px;\r\n  margin-top: 63px;\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #ffffff;\r\n  line-height: 144px;\r\n}\r\n\r\n.nhp {\r\n  text-align: center;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: 500;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n  margin-top: 8px;\r\n}\r\n\r\n.contents {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: url(\"../../assets/image/zichanbeijin.png\");\r\n  background-size: 100% 100%;\r\n  width: 1863px;\r\n  height: 868px;\r\n  z-index: 99999;\r\n  padding-left: 34px;\r\n  padding-right: 22px;\r\n  padding-top: 21px;\r\n}\r\n\r\n.toubu {\r\n  width: 100%;\r\n\r\n  position: relative;\r\n}\r\n\r\n.el-select {\r\n  margin-top: -1px;\r\n  margin-left: 10px;\r\n  background: #00203d;\r\n  border-radius: 3px;\r\n  border: 1px solid #3e89db;\r\n\r\n  /deep/.el-select__wrapper {\r\n    background: #00203d !important;\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper .is-hovering:not {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__wrapper:hover {\r\n    box-shadow: none;\r\n  }\r\n\r\n  /deep/.el-select__placeholder.is-transparent {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select__placeholder {\r\n    color: #2cc1ff;\r\n  }\r\n\r\n  /deep/.el-select-dropdown__item.is-hovering {\r\n    background-color: #2cc1ff !important;\r\n  }\r\n}\r\n\r\n.sp {\r\n  margin-top: -5px;\r\n  margin-left: 12px;\r\n  font-family: Alibaba PuHuiTi;\r\n  font-weight: bold;\r\n  font-size: 21px;\r\n  color: #2cc1ff;\r\n}\r\n\r\n.img1sss {\r\n  cursor: pointer;\r\n  width: 15px;\r\n  height: 15px;\r\n}\r\n</style>"], "mappings": ";;;AAkEA,OAAOA,SAAQ,MAAO,oCAAoC;AAC1D,OAAOC,SAAQ,MAAO,mCAAmC;AACzD,OAAOC,WAAU,MAAO,yCAAyC;AACjE,OAAOC,MAAK,MAAO,0CAA0C;AAC7D,OAAOC,OAAM,MAAO,2CAA2C;AAC/D,OAAOC,MAAK,MAAO,gCAAgC;AACnD,OAAOC,QAAO,MAAO,8CAA8C;AACnE,OAAOC,YAAW,MAAO,gDAAgD;AACzE,OAAOC,YAAW,MAAO,oCAAoC;AAC7D,OAAOC,YAAW,MAAO,sCAAsC;AAC/D,OAAOC,YAAW,MAAO,gDAAgD;AACzE,OAAOC,YAAW,MAAO,sCAAsC;AAC/D,OAAOC,YAAW,MAAO,sCAAsC;AAC/D;AACA,OAAOC,YAAW,MAAO,sCAAsC;AAC/D,OAAOC,QAAO,MAAO,mCAAmC;AACxD,OAAOC,WAAU,MAAO,qCAAqC;AAC7D,OAAOC,QAAO,MAAO,0CAA0C;AAC/D,SACEC,aAAa,EACbC,gBAAgB,EAChBC,oBAAoB,QACf,iBAAiB;AACxB,eAAe;EACbC,UAAU,EAAE;IAEVd,QAAQ;IACRU,QAAQ;IACRhB,SAAS;IACTK,MAAM;IACNH,WAAW;IACXK,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,QAAQ;IACRb,SAAS;IACTE,MAAM;IACNC,OAAO;IACPW;EACF,CAAC;EACDM,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACnBC,MAAM,EAAE,CACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO;MAEX,CAAC;MACDC,WAAW,EAAE;QACXC,IAAI,EAAE,MAAM;QACZF,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QACpBG,KAAK,EAAE,CACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR;QACDC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,IAAI;UACZZ,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACvD,CAAC,EACD;UACES,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,IAAI;UACZZ,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACvD,CAAC;MAEL,CAAC;MACDa,WAAW,EAAE;QACXP,IAAI,EAAE,QAAQ;QACdF,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QACpBG,KAAK,EAAE,CACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACR;QACDC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,IAAI;UACZZ,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;QACzD,CAAC,EACD;UACES,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,MAAM;UACZC,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,IAAI;UACZZ,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QAEvD,CAAC;MAEL,CAAC;MACDc,WAAW,EAAE;QACXX,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChBC,MAAM,EAAE,CACN,IAAI,EACJ,IAAI,EACJ,KAAK;MAET,CAAC;MACDW,UAAU,EAAE;QACVC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;QACrBC,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAE;QACtDC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC1CC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;MAC3C,CAAC;MACDC,UAAU,EAAE;QACVJ,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC;QACnBC,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAE;QACtDC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC1CC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;MAC3C,CAAC;MACDE,UAAU,EAAE;QACVL,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;QACvBC,SAAS,EAAE,CACT,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CAER;QACDC,UAAU,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACrDC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;MACvD,CAAC;MACDG,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,CACP;QACEpB,KAAK,EAAE,IAAI;QACXqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,MAAM;QACbqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,MAAM;QACbqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,MAAM;QACbqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,MAAM;QACbqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,MAAM;QACbqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,OAAO;QACdqB,KAAK,EAAE;MACT,CAAC,CACF;MACDC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEvB,KAAK,EAAE,IAAI;QACXqB,KAAK,EAAE;MACT,CAAC,CACF;MACDG,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEzB,KAAK,EAAE,IAAI;QACXqB,KAAK,EAAE;MACT,CAAC,CACF;MACDK,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACE3B,KAAK,EAAE,IAAI;QACXqB,KAAK,EAAE;MACT,CAAC,CACF;MACDG,YAAY,EAAE,KAAK;MACnBI,QAAQ,EAAE,CACR;QACE5B,KAAK,EAAE,KAAK;QACZqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,KAAK;QACZqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,KAAK;QACZqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,KAAK;QACZqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,KAAK;QACZqB,KAAK,EAAE;MACT,CAAC,EACD;QACErB,KAAK,EAAE,KAAK;QACZqB,KAAK,EAAE;MACT,CAAC,CACF;MACDQ,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,CACV;QACExB,IAAI,EAAE,MAAM;QACZN,KAAK,EAAE,EAAE;QACT+B,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZN,KAAK,EAAE,EAAE;QACT+B,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZN,KAAK,EAAE,EAAE;QACT+B,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZN,KAAK,EAAE,EAAE;QACT+B,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,CACF;MACDC,WAAW,EAAE,CACX;QACE5B,IAAI,EAAE,MAAM;QACZN,KAAK,EAAE,EAAE;QACT+B,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZN,KAAK,EAAE,EAAE;QACT+B,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZN,KAAK,EAAE,EAAE;QACT+B,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACE3B,IAAI,EAAE,MAAM;QACZN,KAAK,EAAE,EAAE;QACT+B,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,CACF;MACD;MACAE,cAAc,EAAE;IAClB,CAAC;EACH,CAAC;EACDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,WAAW,CAAC;IACjB;IACAC,WAAW,CAAC,MAAM;MAChB,IAAI,CAACF,iBAAiB,CAAC,CAAC;IAC1B,CAAC,EAAE,KAAK,CAAC;EACX,CAAC;EACDG,OAAO,EAAE;IACP,MAAMH,iBAAiBA,CAAA,EAAG;MACxB,IAAI;QACF,MAAMI,GAAE,GAAI,MAAM9C,oBAAoB,CAAC;UACrC+C,QAAQ,EAAE,IAAI;UACdC,WAAW,EAAE,CAAC;UACdC,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF,IAAIH,GAAG,CAACI,IAAG,KAAM,GAAE,IAAKJ,GAAG,CAACK,IAAI,EAAE;UAChC;UACA,MAAMC,eAAc,GAAI;YACtBC,IAAI,EAAE,EAAE;YACRC,MAAM,EAAE,EAAE;YACVC,IAAI,EAAE,EAAE;YACRC,IAAI,EAAE;YACN;UACF,CAAC;;UAED;UACA,MAAMC,KAAI,GAAI,CAAC,CAAC;UAChB;UACAC,MAAM,CAACC,IAAI,CAACP,eAAe,CAAC,CAACQ,OAAO,CAAEhD,IAAI,IAAK;YAC7C6C,KAAK,CAAC7C,IAAI,IAAI;cACZiD,KAAK,EAAE,CAAC;cACRC,UAAU,EAAE;YACd,CAAC;UACH,CAAC,CAAC;;UAEF;UACAhB,GAAG,CAACK,IAAI,CAACS,OAAO,CAAEG,IAAI,IAAK;YACzB,IAAIN,KAAK,CAACM,IAAI,CAACC,eAAe,CAAC,EAAE;cAC/BP,KAAK,CAACM,IAAI,CAACC,eAAe,CAAC,CAACH,KAAK,EAAE;cACnC,IAAIE,IAAI,CAACE,MAAK,KAAM,GAAG,EAAE;gBACvBR,KAAK,CAACM,IAAI,CAACC,eAAe,CAAC,CAACF,UAAU,EAAE;cAC1C;YACF;UACF,CAAC,CAAC;;UAEF;UACA,IAAI,CAAC3D,YAAW,GAAIuD,MAAM,CAACQ,OAAO,CAACT,KAAK,CAAC,CAACU,GAAG,CAC3C,CAAC,CAACC,QAAQ,EAAEC,KAAK,CAAC,MAAM;YACtB3C,KAAK,EAAE,GAAG0C,QAAQ,IAAIC,KAAK,CAACR,KAAK,IAAIT,eAAe,CAACgB,QAAQ,CAAC,GAAG;YACjE/D,KAAK,EAAEgE,KAAK,CAACR;UACf,CAAC,CACH,CAAC;UAEDS,OAAO,CAACC,GAAG,CAAC,IAAI,CAACpE,YAAY,EAAE,QAAQ,CAAC;QAC1C;MACF,EAAE,OAAOqE,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC;IACD,MAAM7B,WAAWA,CAAA,EAAG;MAClB,IAAI;QACF,MAAMG,GAAE,GAAI,MAAM9C,oBAAoB,CAAC;UACrC+C,QAAQ,EAAE,IAAI;UACdC,WAAW,EAAE,CAAC;UACdC,QAAQ,EAAE,GAAG;UACbwB,WAAW,EAAE,MAAM;UACnBC,UAAU,EAAE;QACd,CAAC,CAAC;QAEF,IAAI5B,GAAG,CAACI,IAAG,KAAM,GAAE,IAAKJ,GAAG,CAACK,IAAI,EAAE;UAChCmB,OAAO,CAACC,GAAG,CAACzB,GAAG,EAAE,QAAQ,CAAC;;UAE1B;UACA,MAAM6B,QAAO,GAAI;YACf,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,CAAC;YACT,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,IAAI,EAAE;UACR,CAAC;;UAED;UACA7B,GAAG,CAACK,IAAI,CAACS,OAAO,CAACG,IAAG,IAAK;YACvB,IAAIA,IAAI,CAACa,UAAU,EAAE;cACnBlB,MAAM,CAACC,IAAI,CAACgB,QAAQ,CAAC,CAACf,OAAO,CAACiB,OAAM,IAAK;gBACvC,IAAId,IAAI,CAACa,UAAU,CAACE,QAAQ,CAACD,OAAO,CAAC,EAAE;kBACrCF,QAAQ,CAACE,OAAO,CAAC,EAAE;gBACrB;cACF,CAAC,CAAC;YACJ;UACF,CAAC,CAAC;;UAEF;UACA,MAAME,gBAAe,GAAIrB,MAAM,CAACQ,OAAO,CAACS,QAAQ,CAAC,CAACR,GAAG,CAAC,CAAC,CAACU,OAAO,EAAER,KAAK,CAAC,KAAK;YAC1E;YACA,MAAMW,YAAW,GAAI;cACnB,IAAI,EAAE,MAAM;cACZ,IAAI,EAAE,MAAM;cACZ,MAAM,EAAE,SAAS;cACjB,MAAM,EAAE,QAAQ;cAChB,IAAI,EAAE,OAAO;cACb,IAAI,EAAE;YACR,CAAC;YAED,OAAO;cACLrE,IAAI,EAAEqE,YAAY,CAACH,OAAO,KAAKA,OAAO;cACtCxE,KAAK,EAAEgE;YACT,CAAC;UACH,CAAC,CAAC;UAEFC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEQ,gBAAgB,CAAC;;UAE1C;UACA,IAAI,CAACvC,cAAa,GAAIuC,gBAAgB;UAEtC,OAAOA,gBAAgB;QACzB;MACF,EAAE,OAAOP,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC;IACDS,KAAKA,CAAA,EAAG;MACN,IAAI,CAACzD,MAAK,GAAI,KAAK;IACrB;EACF;AACF,CAAC", "ignoreList": []}]}