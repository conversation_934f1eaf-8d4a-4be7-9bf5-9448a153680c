<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        barWidth: 15,
        title: {
          text: "kwh",
          left: "70",
          top: "1",
          textStyle: {
            color: "#fff",
            fontSize: 14,
            fontWeight: "400",
          },
        },
        xAxis: {
          type: "value",
          splitLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            //  改变x轴颜色
            lineStyle: {
              color: "#26D9FF",
            },
          },
          axisLabel: {
            //  改变x轴字体颜色和大小
            textStyle: {
              color: "#fff",
              fontSize: 12,
            },
          },
        },
        grid: {
          //图表的位置
          top: "10%",
          left: "3%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },
        yAxis: {
          type: "category",
          data: [
            "空压用电",
            "空调用电",
            "氮气用电",
            "真空用电",
            "照明用电",
            "生产用电",
            "公共用电",
            "排废用电",
            "办公区用电",
          ],
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            //  改变y轴颜色
            lineStyle: {
              color: "#26D9FF",
            },
          },
          axisLabel: {
            //  改变y轴字体颜色和大小
            //formatter: '{value} m³ ', //  给y轴添加单位
            textStyle: {
              color: "#fff",
              fontSize: 12,
            },
          },
        },
        series: [
          {
            name: "数据内框",
            type: "bar",
            itemStyle: {
              normal: {
                barBorderRadius: 0,
                color: "#00b5eb",
              },
            },
            barWidth: 10,
            data: [11, 33, 77, 39, 55, 22, 11, 38, 45],
            label: {
              show: true,
              position: "right", // 标签位置，可以根据需要调整
              color: "#fff", // 标签文字颜色
              fontSize: 12, // 标签文字大小
            },
          },
          {
            name: "外框",
            type: "bar",
            itemStyle: {
              normal: {
                barBorderRadius: 0,
                color: "rgba(255, 255, 255, 0.14)", //rgba设置透明度0.14
              },
            },
            barGap: "-100%",
            z: 0,
            barWidth: 10,
            data: [100, 100, 100, 100, 100, 100, 100, 100, 100],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 668px;
  height: 250px;
}

@media (max-height: 1080px) {
  .echart {
    width: 668px;
    height: 250px !important;
  }
}
</style>