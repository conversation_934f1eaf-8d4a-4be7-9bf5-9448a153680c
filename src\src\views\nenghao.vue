<template>
  <div class="contents" v-if="isshow">
    <div class="toubu">
      <div
        style="margin-left: 20px; display: flex; align-items: center"
        v-if="false"
      >
        <div style="display: flex; width: 100%; align-items: center">
          <span class="sp">当前位置：</span>
          <el-select
            class="el-select"
            v-model="selectvalue1"
            placeholder="selectvalue1"
            style="width: 64px; height: 35.1px"
            @change="handleChange"
          >
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            class="el-select"
            v-model="selectvalue2"
            placeholder="selectvalue2"
            style="width: 64px; height: 35.1px"
            @change="handleChange"
          >
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            class="el-select"
            v-model="selectvalue3"
            placeholder="selectvalue3"
            style="width: 78px; height: 35.1px"
            @change="handleChange"
          >
            <el-option
              v-for="item in options3"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <img
          v-if="isshow"
          class="img1sss"
          @click="anniu()"
          src="../assets/image/table-x.png"
          alt=""
        />
      </div>

      <div class="all">
        <div class="all1">
          <Titles class="ltitle" tit="大型仪器平台概况">
            <div class="nenghao">累计总能耗:</div>
            <p class="nhp">{{ totalConsumption.monthly.toFixed(1) }} kwh</p>
            <div class="nh">
              <img class="nhimg" src="../assets/image/nenghao1.png" alt="" />
              <div class="nhtit">
                <p class="p11">{{ totalConsumption.daily.toFixed(1) }}kwh</p>
                <p class="p2">本日累计能耗</p>
              </div>
              <!-- <div class="nhtit1">
                <img class="nhimg1" src="../assets/image/nhxia.png" alt="" />
                <p class="pp1">{{ ((totalConsumption.daily - totalConsumption.daily) / totalConsumption.daily * 100).toFixed(1) }}%</p>
              </div> -->
            </div>
            <div class="nh">
              <img class="nhimg" src="../assets/image/nenghao1.png" alt="" />
              <div class="nhtit">
                <p class="p11">{{ totalConsumption.weekly.toFixed(1) }}kwh</p>
                <p class="p2">近7日累计能耗</p>
              </div>
              <!-- <div class="nhtit1">
                <img class="nhimg1" src="../assets/image/nhxia.png" alt="" />
                <p class="pp1">{{ ((totalConsumption.weekly - totalConsumption.weekly) / totalConsumption.weekly * 100).toFixed(1) }}%</p>
              </div> -->
            </div>
            <div class="nh">
              <img class="nhimg" src="../assets/image/nenghao2.png" alt="" />
              <div class="nhtit">
                <p class="p12">{{ totalConsumption.monthly.toFixed(1) }}kwh</p>
                <p class="p2">近30日累计能耗</p>
              </div>
              <div class="nht">
                <!-- <div class="nhtit1">
                  <img
                    class="nhimg1"
                    src="../assets/image/nhshang.png"
                    alt=""
                  />
                  <p class="pp2">{{ ((totalConsumption.monthly - totalConsumption.monthly) / totalConsumption.monthly * 100).toFixed(1) }}%</p>
                </div> -->
                <!-- <p class="pp">环比</p> -->
              </div>
            </div>
          </Titles>
          <!-- <Titles class="ltitle" style="margin-top: 20px" tit="电耗费用">
            <div class="shinei">
              <Electricity2
                :yesterday-fee="electricityFees.yesterday"
                :monthly-fee="electricityFees.monthly"
                :yearly-fee="electricityFees.yearly"
              ></Electricity2>
            </div>
          </Titles> -->
        </div>
        <!-- <div class="line1"></div> -->
        <div class="all2">
          <!-- <Titles class="ltitle1" tit="峰平谷用电量">
            <div class="shinei">
              <Electricity8></Electricity8>
            </div>
          </Titles> -->

          <Titles class="ltitle1" tit="用电量排名">
            <div class="shinei">
              <Electricity3
                :electricity-data="electricityUsageData"
              ></Electricity3>
            </div>
          </Titles>
          <!-- <Titles class="ltitle1" tit="分区用电量">
            <div class="shinei">
              <Electricity4 :fee-data="electricityFeeData"></Electricity4>
            </div>
          </Titles> -->
        </div>
        <div class="all3">
          <div>
            <Titles class="ltitle1" tit="抄电表记录">
              <div class="shinei">
                <div class="table-container">
                  <el-table
                    :data="meterReadings"
                    style="width: 100%; background: transparent"
                    :header-cell-style="{
                      background: '#2A363F',
                      color: '#fff',
                      borderColor: '#1e415c',
                    }"
                    :cell-style="{
                      background: '#2A363F',
                      color: '#fff',
                      borderColor: '#1e415c',
                    }"
                    height="320"
                  >
                    <el-table-column
                      prop="roomtag"
                      label="房间标识"
                      align="center"
                      width="120"
                    />
                    <el-table-column
                      prop="zhaddress"
                      label="住户地址"
                      align="center"
                      width="180"
                    />
                    <el-table-column
                      prop="readvalue"
                      label="抄表值"
                      align="center"
                      width="120"
                    />
                    <el-table-column
                      prop="readtime"
                      label="抄表时间"
                      align="center"
                      width="180"
                    />
                  </el-table>
                </div>
              </div>
            </Titles>
          </div>
          <Titles class="ltitle1" style="margin-top:15px" tit="用电量记录">
            <div class="shinei">
              <div class="title-container">
                <div class="more-btn" @click="showDetailDialog">
                  <span>更多</span>
                  <i class="el-icon-arrow-right"></i>
                </div>
              </div>
              <div class="table-container">
                <el-table
                  :data="electricityUsageData"
                  style="width: 100%; background: transparent"
                  :header-cell-style="{
                    background: '#2A363F',
                    color: '#fff',
                    borderColor: '#38444C',
                  }"
                  :cell-style="{
                    background: '#2A363F',
                    color: '#fff',
                    borderColor: '#1e415c',
                  }"
                  height="400"
                >
                  <el-table-column
                    prop="roomtag"
                    label="房间标识"
                    align="center"
                    width="120"
                  />
                  <el-table-column
                    prop="zhaddress"
                    label="住户地址"
                    align="center"
                    width="180"
                  />
                  <el-table-column
                    prop="ylvalue"
                    label="用电量(kwh)"
                    align="center"
                    width="120"
                  >
                    <!-- <template slot-scope="scope">
                      {{ parseFloat(scope.row.ylvalue).toFixed(2) }}
                    </template> -->
                  </el-table-column>
                  <el-table-column
                    prop="endtime"
                    label="抄表时间"
                    align="center"
                    width="180"
                  />
                </el-table>
              </div>
            </div>
          </Titles>
        </div>
      </div>
    </div>

    <!-- 自定义弹窗 -->
    <div
      v-if="dialogVisible"
      class="custom-modal-overlay"
      @click.self="dialogVisible = false"
    >
      <div class="custom-modal">
        <div class="modal-header">
          <span class="modal-title">用电量详细记录</span>
          <div class="header-buttons">
            <el-button
              type="text"
              class="close-text"
              @click="dialogVisible = false"
              >关闭</el-button
            >
            <i
              class="el-icon-close close-btn"
              @click="dialogVisible = false"
            ></i>
          </div>
        </div>
        <div class="modal-content">
          <!-- 搜索条件 -->
          <div class="search-container">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              style="width: 380px; margin-right: 15px"
            >
            </el-date-picker>
            <!-- <el-date-picker
                v-model="dateRange"
                type="daterange"
                align="right"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
              > -->
            <!-- </el-date-picker> -->

            <el-button type="primary" @click="searchData">查询</el-button>
            <!-- <el-button type="success" @click="exportToExcel">导出</el-button> -->
          </div>

          <!-- 详细数据表格 -->
          <el-table
            :data="detailData"
            style="width: 100%; margin-top: 20px"
            :header-cell-style="{
              background: '#2C4255',
              color: '#fff',
              borderColor: '#1e415c',
            }"
            :cell-style="{
              background: '#2C4255',
              color: '#fff',
              borderColor: '#1e415c',
            }"
            height="500"
          >
            <el-table-column
              prop="roomtag"
              label="房间标识"
              align="center"
              width="180"
            />
            <el-table-column
              prop="zhaddress"
              label="住户地址"
              align="center"
              width="180"
            />
            <el-table-column
              prop="startcode"
              label="起码"
              align="center"
              width="180"
            />
            <el-table-column
              prop="endcode"
              label="止码"
              align="center"
              width="180"
            />
            <el-table-column
              prop="ylvalue"
              label="用电量(kwh)"
              align="center"
              width="180"
            />
            <el-table-column
              prop="jfmx"
              label="缴费明细"
              align="center"
              width="180"
            />

            <el-table-column
              prop="endtime"
              label="抄表时间"
              align="center"
              width="181"
            />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as XLSX from "xlsx";
import Electricity from "../components/echarts/dianbiao/biao1.vue";
import biao1s from "../components/echarts/dianbiao/biao1s.vue";
import biao1ss from "../components/echarts/dianbiao/biao1ss.vue";
import Titles from "../components/common/Titles.vue";

import Electricity2 from "../components/echarts/dianbiao/Electricity2.vue";
import Electricity3 from "../components/echarts/dianbiao/Electricity3.vue";
import Electricity4 from "../components/echarts/dianbiao/Electricity4.vue";
import Electricity5 from "../components/echarts/dianbiao/Electricity5.vue";
import Electricity6 from "../components/echarts/dianbiao/Electricity6.vue";
import Electricity7 from "../components/echarts/dianbiao/Electricity7.vue";
import Electricity8 from "../components/echarts/dianbiao/Electricity8.vue";
import huanxing from "@/components/echarts/xiaobingtu.vue";
import axios from "axios";

export default {
  components: {
    Titles,
    Electricity,
    Electricity2,
    Electricity3,
    Electricity4,
    Electricity5,
    Electricity6,
    Electricity7,
    Electricity8,
    huanxing,
    biao1s,
    biao1ss,
  },
  data() {
    return {
      isshow: true,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      options: [
        {
          value: "总览",
          label: "总览",
        },
        {
          value: "能耗分析",
          label: "能耗分析",
        },
        {
          value: "能流分析",
          label: "能流分析",
        },
        {
          value: "设备状态",
          label: "设备状态",
        },
        {
          value: "一键抄表",
          label: "一键抄表",
        },
        {
          value: "费用管理",
          label: "费用管理",
        },
        {
          value: "碳排放管理",
          label: "碳排放管理",
        },
      ],
      selectvalue2: "B3",
      options2: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B3",
      options3: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue1: "B3",
      options1: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B1栋",
      options4: [
        {
          value: "B1栋",
          label: "B1栋",
        },
        {
          value: "B2栋",
          label: "B2栋",
        },
        {
          value: "B3栋",
          label: "B3栋",
        },
        {
          value: "B4栋",
          label: "B4栋",
        },
        {
          value: "W1栋",
          label: "W1栋",
        },
        {
          value: "W2栋",
          label: "W2栋",
        },
      ],
      selectvalue4: "B1栋",
      optionData: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
      optionData1: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
      token: {
        systemnum: "",
        tokenvalue: "",
        expiretime: "",
      },
      baseURL: "/power", // Replace with actual server address
      // baseURL: 'http://*************:8080',
      isTokenValid: false,
      totalConsumption: {
        daily: 0,
        weekly: 0,
        monthly: 0,
      },
      totalElectricityFee: 0,
      roomtag: "", // Add your roomtag here if you want to query specific user
      electricityFees: {
        yesterday: 0,
        monthly: 0,
        yearly: 0,
      },
      meterReadings: [], // 新增抄表记录数据
      electricityUsageData: [], // 新增用电量数据
      
      dialogVisible: false, // 修改为 false，默认关闭
      dateRange: "",
      detailData: [],
      electricityFeeData: [], // 新增电费数据
    };
  },
  methods: {
    anniu() {
      this.isshow = false;
    },
    async getToken() {
      try {
        const response = await axios.get(
          `${this.baseURL}/api/ztwyPower/getToken`,
          {
            params: {
              systemnum: "********************************",
            },
          }
        );

        if (response.data.errcode === 0) {
          const { systemnum, tokenvalue, expiretime } =
            response.data.resultvalue;
          this.token = {
            systemnum,
            tokenvalue,
            expiretime,
          };
          this.isTokenValid = true;

          // 存储 token 和获取时间
          const tokenData = {
            ...this.token,
            timestamp: new Date().getTime(),
          };
          localStorage.setItem("powerToken", JSON.stringify(tokenData));

          console.log("Token updated successfully:", this.token);
        } else {
          console.error("Failed to get token:", response.data.errmsg);
          this.isTokenValid = false;
        }
      } catch (error) {
        console.error("Error getting token:", error);
        this.isTokenValid = false;
      }
    },
    checkTokenValidity() {
      const storedToken = localStorage.getItem("powerToken");
      if (storedToken) {
        const tokenData = JSON.parse(storedToken);
        const expireTime = new Date(tokenData.expiretime).getTime();
        const currentTime = new Date().getTime();
        const tokenTimestamp = tokenData.timestamp;

        // 检查 token 是否过期或距离上次获取是否超过5分钟
        if (
          currentTime < expireTime &&
          currentTime - tokenTimestamp < 5 * 60 * 1000
        ) {
          this.token = {
            systemnum: tokenData.systemnum,
            tokenvalue: tokenData.tokenvalue,
            expiretime: tokenData.expiretime,
          };
          this.isTokenValid = true;
          return true;
        }
      }
      return false;
    },
    async getElectricityUsage(startTime, endTime) {
      if (!this.isTokenValid) {
        await this.getToken();
      }

      try {
        const response = await axios.post(
          `${this.baseURL}/api/ztwyPower/getYdlByTime`,
          {
            tokenvalue: this.token.tokenvalue,
            starttime: startTime,
            endtime: endTime,
            roomtag: this.roomtag, // Optional: if empty, will return all users' data
          }
        );

        if (response.data.errcode === 0) {
          // Calculate total consumption by summing ylvalue
          const totalConsumption = response.data.resultvalue.reduce(
            (sum, item) => {
              return sum + parseFloat(item.ylvalue || 0);
            },
            0
          );
          return totalConsumption;
        } else {
          console.error(
            "Failed to get electricity usage:",
            response.data.errmsg
          );
          return 0;
        }
      } catch (error) {
        console.error("Error getting electricity usage:", error);
        return 0;
      }
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    async getPaymentStatistics(startTime, endTime) {
      if (!this.isTokenValid) {
        await this.getToken();
      }

      try {
        const response = await axios.post(
          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,
          {
            tokenvalue: this.token.tokenvalue,
            starttime: startTime,
            endtime: endTime,
            roomtag: this.roomtag,
          }
        );

        if (response.data.errcode === 0) {
          const totalFee = response.data.resultvalue.reduce((sum, item) => {
            return sum + parseFloat(item.zdf || 0);
          }, 0);
          return totalFee;
        } else {
          console.error(
            "Failed to get payment statistics:",
            response.data.errmsg
          );
          return 0;
        }
      } catch (error) {
        console.error("Error getting payment statistics:", error);
        return 0;
      }
    },
    async updateConsumptionData() {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const weekAgo = new Date(today);
      weekAgo.setDate(weekAgo.getDate() - 7);
      const monthAgo = new Date(today);
      monthAgo.setDate(monthAgo.getDate() - 30);

      // Get daily consumption
      this.totalConsumption.daily = await this.getElectricityUsage(
        this.formatDate(yesterday),
        this.formatDate(now)
      );

      // Get weekly consumption
      this.totalConsumption.weekly = await this.getElectricityUsage(
        this.formatDate(weekAgo),
        this.formatDate(now)
      );

      // Get monthly consumption
      this.totalConsumption.monthly = await this.getElectricityUsage(
        this.formatDate(monthAgo),
        this.formatDate(now)
      );

      // Get total electricity fee
      this.totalElectricityFee = await this.getPaymentStatistics(
        this.formatDate(monthAgo),
        this.formatDate(now)
      );
    },
    async updateFeeData() {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
      const yearStart = new Date(today.getFullYear(), 0, 1);

      // Get yesterday's fee
      this.electricityFees.yesterday = await this.getPaymentStatistics(
        this.formatDate(yesterday),
        this.formatDate(today)
      );

      // Get monthly fee
      this.electricityFees.monthly = await this.getPaymentStatistics(
        this.formatDate(monthStart),
        this.formatDate(now)
      );

      // Get yearly fee
      this.electricityFees.yearly = await this.getPaymentStatistics(
        this.formatDate(yearStart),
        this.formatDate(now)
      );
    },
    async getMeterReadings() {
      if (!this.isTokenValid) {
        await this.getToken();
      }

      try {
        const now = new Date();
        const sevenDaysAgo = new Date(now);
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const response = await axios.post(
          `${this.baseURL}/api/ztwyPower/getAllDbValue`,
          {
            tokenvalue: this.token.tokenvalue,
            starttime: this.formatDate(sevenDaysAgo),
            endtime: this.formatDate(now),
            tag: 0, // 返回最后一次抄表记录
            nodeid: 0, // 默认为0，返回全部
            roomtag: this.roomtag,
          }
        );

        if (response.data.errcode === 0) {
          this.meterReadings = response.data.resultvalue;
          console.log(
            "Meter readings retrieved successfully:",
            this.meterReadings
          );
        } else {
          console.error("Failed to get meter readings:", response.data.errmsg);
        }
      } catch (error) {
        console.error("Error getting meter readings:", error);
      }
    },
    async getElectricityUsageData() {
      if (!this.isTokenValid) {
        await this.getToken();
      }

      try {
        const now = new Date();
        const sevenDaysAgo = new Date(now);
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const response = await axios.post(
          `${this.baseURL}/api/ztwyPower/getYdlByTime`,
          {
            tokenvalue: this.token.tokenvalue,
            starttime: this.formatDate(sevenDaysAgo),
            endtime: this.formatDate(now),
            roomtag: this.roomtag,
          }
        );

        if (response.data.errcode === 0) {
          this.electricityUsageData = response.data.resultvalue
            .map((item) => ({
              ...item,
              ylvalue: parseFloat(item.ylvalue || 0),
            }))
            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));
          console.log(
            "Electricity usage data retrieved successfully:",
            this.electricityUsageData
          );
        } else {
          console.error(
            "Failed to get electricity usage:",
            response.data.errmsg
          );
        }
      } catch (error) {
        console.error("Error getting electricity usage:", error);
      }
    },
    showDetailDialog() {
      this.dialogVisible = true;
      this.detailData = []; // 清空数据
      // 默认显示最近一天的数据
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24); // 最近一天
      this.dateRange = [
        this.formatDate(start).split(" ")[0],
        this.formatDate(end).split(" ")[0],
      ];
      this.searchData(); // 自动查询最近一天的数据
    },
    async searchData() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message.warning("请选择日期范围");
        return;
      }

      try {
        if (!this.isTokenValid) {
          await this.getToken();
        }

        const response = await axios.post(
          `${this.baseURL}/api/ztwyPower/getYdlByTime`,
          {
            tokenvalue: this.token.tokenvalue,
            starttime: `${this.dateRange[0]} 00:00:00`,
            endtime: `${this.dateRange[1]} 23:59:59`,
            roomtag: this.roomtag,
          }
        );

        if (response.data.errcode === 0) {
          if (
            response.data.resultvalue &&
            response.data.resultvalue.length > 0
          ) {
            this.detailData = response.data.resultvalue
              .map((item) => ({
                roomtag: item.roomtag || "",
                zhaddress: item.zhaddress || "",
                startcode: item.startcode
                  ? parseFloat(item.startcode).toFixed(1)
                  : "0.0",
                endcode: item.endcode
                  ? parseFloat(item.endcode).toFixed(1)
                  : "0.0",
                ylvalue: item.ylvalue
                  ? parseFloat(item.ylvalue).toFixed(2)
                  : "0.00",
                jfmx: item.jfmx || "",
                endtime: item.endtime ? item.endtime.replace(/-/g, "/") : "",
              }))
              .sort((a, b) => new Date(b.endtime) - new Date(a.endtime));
            this.$message.success("查询成功");
          } else {
            this.detailData = [];
            this.$message.warning("所选时间范围内无数据");
          }
        } else {
          this.$message.error("获取数据失败：" + response.data.errmsg);
          this.detailData = [];
        }
      } catch (error) {
        this.$message.error("获取数据失败：" + (error.message || "未知错误"));
        this.detailData = [];
      }
    },
    exportToExcel() {
      if (!this.detailData || !this.detailData.length) {
        this.$message.warning("暂无数据可导出");
        return;
      }

      try {
        // 准备要导出的数据
        const exportData = this.detailData.map((item) => ({
          房间标识: item.roomtag || "",
          住户地址: item.zhaddress || "",
          起码: item.startcode ? parseFloat(item.startcode).toFixed(1) : "0.0",
          止码: item.endcode ? parseFloat(item.endcode).toFixed(1) : "0.0",
          "用电量(kwh)": item.ylvalue
            ? parseFloat(item.ylvalue).toFixed(2)
            : "0.00",
          缴费明细: item.jfmx || "",
          抄表时间: item.endtime ? item.endtime.replace(/-/g, "/") : "",
        }));

        // 创建工作簿并设置数据
        const ws = XLSX.utils.json_to_sheet(exportData);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, "用电量记录");

        // 设置列宽
        ws["!cols"] = [
          { wch: 15 }, // 房间标识
          { wch: 20 }, // 住户地址
          { wch: 12 }, // 起码
          { wch: 12 }, // 止码
          { wch: 15 }, // 用电量
          { wch: 15 }, // 缴费明细
          { wch: 20 }, // 抄表时间
        ];

        // 直接使用 XLSX.writeFile 导出文件
        const fileName = `用电量记录_${this.dateRange[0]}_${this.dateRange[1]}.xlsx`;
        XLSX.writeFile(wb, fileName);

        this.$message.success("导出成功");
      } catch (error) {
        console.error("Export error:", error);
        this.$message.error("导出失败：" + (error.message || "未知错误"));
      }
    },
    async getElectricityFeeData() {
      if (!this.isTokenValid) {
        await this.getToken();
      }

      try {
        const now = new Date();
        const sevenDaysAgo = new Date(now);
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        const response = await axios.post(
          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,
          {
            tokenvalue: this.token.tokenvalue,
            starttime: this.formatDate(sevenDaysAgo),
            endtime: this.formatDate(now),
            roomtag: this.roomtag,
          }
        );

        if (response.data.errcode === 0) {
          this.electricityFeeData = response.data.resultvalue
            .map((item) => ({
              ...item,
              zdf: parseFloat(item.zdf || 0),
            }))
            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));
          console.log(
            "Electricity fee data retrieved successfully:",
            this.electricityFeeData
          );
        } else {
          console.error(
            "Failed to get electricity fee data:",
            response.data.errmsg
          );
        }
      } catch (error) {
        console.error("Error getting electricity fee data:", error);
      }
    },
  },
  async created() {
    // 初始化获取 token
    if (!this.checkTokenValidity()) {
      await this.getToken();
    }

    // 更新数据
    await this.updateConsumptionData();
    await this.updateFeeData();
    await this.getMeterReadings();
    await this.getElectricityUsageData();
    await this.getElectricityFeeData();

    // 每5分钟更新一次 token
    setInterval(async () => {
      await this.getToken();
    }, 5 * 60 * 1000);

    // 每5分钟更新一次数据
    setInterval(async () => {
      await this.updateConsumptionData();
      await this.updateFeeData();
      await this.getMeterReadings();
      await this.getElectricityUsageData();
      await this.getElectricityFeeData();
    }, 5 * 60 * 1000);
  },
};
</script>

<style lang="less" scoped>
.all {
  display: flex;
  flex-direction: row;
  margin-top: 5px;

  .zong {
    display: flex;
    flex-direction: row;
    margin-top: 10px;
    .echart1,
    .echart2 {
      flex: 1;

      .center {
        margin-top: -24px;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: 400;
        font-size: 17px;
        color: #00ffb6;
        text-align: center;
        margin-bottom: 10px;
      }

      .btn {
        width: 133px;
        height: 31px;
        border: 1px solid #2d6cb0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        border-radius: 30px;
        margin-left: 7%;
      }
    }
  }

  .ltitle1 {
    margin-top: 10px;
    position: relative;
  }

  .line1 {
    width: 2px;
    height: 823px;
    opacity: 0.64;
    background-color: #204964;
  }

  .all1 {
    flex: 557;

    .nenghao {
      width: 257px;
      height: 183px;
      background: url("../assets/image/nenghao.png");
      background-size: 100% 100%;
      margin-left: 100px;
      margin-top: 45px;
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 20px;
      color: #ffffff;
      line-height: 213px;
    }

    .nhp {
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 52px;
      color: #2cc1ff;
      margin-top: 8px;
    }

    .nh {
      margin-left: 24px;
      margin-top: 32px;
      width: 423px;
      height: 105px;
      border: 1px solid #364d5a;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 72px;
      margin-bottom: 5px;
      // justify-content: space-evenly;

      .nhimg {
        width: 107px;
        height: 90px;
        margin-right: 35px;
      }

      .nhtit {
        width: 148px;
        margin-left: 10px;
        margin-top: 3px;

        .p11 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 30px;
          color: #7acfff;
        }

        .p12 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 30px;
          color: #ffa170;
        }

        .p2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 20px;
          color: #ffffff;
        }
      }

      .nhtit1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-left: 35px;

        .nhimg1 {
          width: 16px;
          height: 20px;
        }

        .pp1 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #0df29b;
        }

        .pp2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffa170;
        }
      }

      .nht {
        margin-top: 10px;
        display: flex;
        flex-direction: column;

        .pp {
          margin-left: 35px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;

          color: #cccccc;
        }
      }
    }
  }

  .all2 {
    margin-left: -52px;
    flex: 627;
    display: flex;
    flex-direction: column;
    .shinei {
      .itemshei {
        display: flex;
        justify-content: space-around;
        .nenghaos {
          width: 227px;
          height: 173px;
          background: url("../assets/image/nenghao.png");
          background-size: 100% 100%;
          text-align: center;
          margin-left: 10px;
          margin-top: 33px;
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 144px;
        }
        .nhps {
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          font-size: 21px;
          color: #2cc1ff;
          margin-top: 8px;
        }
      }
    }
  }

  .all3 {
    flex: 658;
    margin-left: 15px;
  }
}

.shinei {
  width: 100%;
  height: 100%;
}
.shuantitle {
  width: 100%;
  display: flex;
  margin-top: 10px;
  .title {
    width: 95%;
    background: url("../assets/image/title.png");
    background-size: 100% 100%;

    height: 25px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 25px;
    color: #ffffff;
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);
    font-style: italic;
    text-align: left;
    line-height: 4px;
    padding-left: 33px;
  }
}
.nenghao {
  width: 167px;
  height: 113px;
  background: url("../assets/image/nenghao.png");
  background-size: 100% 100%;
  text-align: center;
  margin-left: 83px;
  // margin-top: 63px;
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 144px;
}
.nhp {
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 25px;
  color: #2cc1ff;
  margin-top: 8px;
  width: 79%;
}

.contents {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../assets/image/zichanbeijin.png");
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;
}
.toubu {
  width: 100%;

  position: relative;
}
.el-select {
  margin-top: -1px;
  margin-left: 10px;
  background: #00203d;
  border-radius: 3px;
  border: 1px solid #3e89db;

  /deep/.el-select__wrapper {
    background: #00203d !important;
    box-shadow: none;
  }

  /deep/.el-select__wrapper .is-hovering:not {
    box-shadow: none;
  }

  /deep/.el-select__wrapper:hover {
    box-shadow: none;
  }

  /deep/.el-select__placeholder.is-transparent {
    color: #2cc1ff;
  }

  /deep/.el-select__placeholder {
    color: #2cc1ff;
  }

  /deep/.el-select-dropdown__item.is-hovering {
    background-color: #2cc1ff !important;
  }
}
.sp {
  margin-top: -5px;
  margin-left: 12px;
  font-family: Alibaba PuHuiTi;
  font-weight: bold;
  font-size: 21px;
  color: #2cc1ff;
}
.img1sss {
  cursor: pointer;
  width: 15px;
  height: 15px;
}

.table-container {
  cursor: pointer;
  .el-table {
    background-color: transparent !important;

    // 设置滚动条样式
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-thumb {
      background: #0a3054;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-track {
      background: #1e415c;
      border-radius: 3px;
    }

    // 设置表格背景透明
    ::v-deep .el-table__body-wrapper {
      background-color: transparent;

      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #0a3054;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: #1e415c;
        border-radius: 3px;
      }
    }
  }
}

.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.custom-modal {
  width: 1300px;
  background: #1B2A47;
  border: 1px solid #00E4FF;
  border-radius: 8px;
  padding: 0;
  
  .modal-header {
    background: #1B2A47;
    border-bottom: 1px solid #00E4FF;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .modal-title {
      color: #00E4FF;
      font-size: 18px;
      font-weight: bold;
    }

    .header-buttons {
      display: flex;
      align-items: center;
      
      .close-text {
        color: #00E4FF;
        margin-right: 15px;
      }

      .close-btn {
        color: #00E4FF;
        font-size: 20px;
        cursor: pointer;
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  .modal-content {
    padding: 20px;
    background: #1B2A47;

    .search-container {
      margin-bottom: 20px;
      
      .el-button--primary {
        background: #1B2A47;
        border: 1px solid #00E4FF;
        color: #00E4FF;
        
        &:hover {
          opacity: 0.8;
        }
      }
    }

    .el-table {
      background: #1B2A47 !important;
      border: 1px solid #00E4FF;
      
      &::before {
        display: none;
      }

      th {
        background: #162442 !important;
        border-bottom: 1px solid #00E4FF !important;
        color: #00E4FF !important;
        font-weight: bold;
      }

      td {
        background: #1B2A47 !important;
        border-bottom: 1px solid rgba(0, 228, 255, 0.2) !important;
        color: #fff !important;
      }

      .el-table__row:hover > td {
        background: #243B6B !important;
      }
    }

    .el-table--border::after {
      display: none;
    }
  }
}

// 修改日期选择器样式
:deep(.el-date-editor) {
  background: #1B2A47;
  border: 1px solid #00E4FF;
  
  .el-range-input {
    background: #1B2A47;
    color: #fff;
  }
  
  .el-range-separator {
    color: #00E4FF;
  }
}

// 修改滚动条样式
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #00E4FF;
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #1B2A47;
}

.title-container {
  position: absolute;
  top: -9px;
  left: 57.5%;
  width: 100%;
  z-index: 1000;

  .more-btn {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #2cc1ff;
    font-size: 20px;

    &:hover {
      opacity: 0.8;
    }

    i {
      margin-left: 5px;
    }
  }
}
</style>
