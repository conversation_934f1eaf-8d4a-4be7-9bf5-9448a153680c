{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\components\\gongqi\\Electricity4.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\components\\gongqi\\Electricity4.vue", "mtime": 1750745518997}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\components\\gongqi\\Electricity4.vue"], "names": [], "mappings": ";AAKA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACX,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC;MACJ,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACX;EACF,CAAC;;EAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE/C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACpE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,EAAE;UACA,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACzB,CAAC;;MAEH,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACT,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACd,CAAC;QACH,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACb,CAAC;QACH,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnC,CAAC;YACH,CAAC;UACH,CAAC;QACH,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC;YACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACX,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC;YACH,CAAC;UACH,CAAC;QACH,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;kBACpD;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC;kBACD;oBACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAClB,CAAC;gBACH,CAAC,CAAC;gBACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cAChB,CAAC;YACH,CAAC;UACH,CAAC;QACH,CAAC;MACH,CAAC;;MAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;EACH,CAAC;AACH,CAAC", "file": "E:/svn/SH-20240918-0186-SFTianjinUniversityH5/src/src/components/gongqi/Electricity4.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"echart\" ref=\"echart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from \"echarts\";\r\n\r\nexport default {\r\n  name: \"IoTequip\",\r\n  props: {\r\n    gasWarningData: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n\r\n  mounted() {\r\n    this.init();\r\n  },\r\n\r\n  watch: {\r\n    gasWarningData: {\r\n      handler() {\r\n        this.$nextTick(() => {\r\n          this.init();\r\n        });\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    initData() { },\r\n    init() {\r\n      const myChart = echarts.init(this.$refs.echart);\r\n\r\n      // 使用传入的数据或默认数据\r\n      const chartData = this.gasWarningData && this.gasWarningData.length > 0\r\n        ? this.gasWarningData\r\n        : [\r\n          { name: \"氧气O₂\", value: 1 },\r\n          { name: \"氢气H₂\", value: 2 },\r\n          { name: \"二氧化碳CO₂\", value: 1 },\r\n          { name: \"一氧化碳CO\", value: 3 },\r\n          { name: \"氨气NH₃\", value: 2 },\r\n          { name: \"甲烷\", value: 1 }\r\n        ];\r\n\r\n      // 提取x轴标签和数据\r\n      const xAxisData = chartData.map(item => item.name);\r\n      const seriesData = chartData.map(item => item.value);\r\n\r\n      const option = {\r\n        color: [\"#3398DB\"],\r\n        title: {\r\n          text: \"个\",\r\n          x: \"6%\",\r\n          y: \"8%\",\r\n          textStyle: {\r\n            color: \"#fff\",\r\n            fontSize: 14,\r\n          },\r\n        },\r\n        legend: {\r\n          data: [\"报警数\"],\r\n          top: \"8%\",\r\n          right: \"40px\",\r\n          itemWidth: 10,\r\n          itemHeight: 10,\r\n          textStyle: {\r\n            color: \"#fff\",\r\n            fontSize: 15\r\n          },\r\n        },\r\n        grid: {\r\n          top: \"18%\",\r\n          bottom: \"6%\",\r\n          left: \"2%\",\r\n          right: \"6%\",\r\n          containLabel: true,\r\n        },\r\n        xAxis: [\r\n          {\r\n            type: \"category\",\r\n            data: xAxisData,\r\n            axisLabel: {\r\n              show: true,\r\n              rotate: 45,  // 设置标签旋转角度为45度\r\n              textStyle: {\r\n                color: \"#fff\", // 将 X 轴标签字体颜色设置为白色\r\n              },\r\n            },\r\n          },\r\n        ],\r\n        yAxis: [\r\n          {\r\n            axisTick: {\r\n              show:false,\r\n              alignWithLabel: false,\r\n            },\r\n            // min:1,\r\n            interval: 1,  // 固定刻度间隔为1\r\n            axisLabel: {\r\n              show: true,\r\n              textStyle: {\r\n                color: \"#fff\",\r\n                fontSize: 15\r\n                // 将 Y 轴标签字体颜色设置为白色\r\n              },\r\n            },\r\n          },\r\n        ],\r\n        series: [\r\n          {\r\n            name: \"报警数\",\r\n            type: \"bar\",\r\n            barWidth: \"40%\",\r\n            data: seriesData,\r\n            itemStyle: {\r\n              normal: {\r\n                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                  {\r\n                    offset: 0,\r\n                    color: \"#66C4FC\",\r\n                  },\r\n                  {\r\n                    offset: 1,\r\n                    color: \"#66C4FC\",\r\n                  },\r\n                ]),\r\n                shadowColor: \"rgba(0, 0, 0, 0.1)\",\r\n                shadowBlur: 10,\r\n              },\r\n            },\r\n          },\r\n        ],\r\n      };\r\n\r\n      myChart.setOption(option);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.echart {\r\n  margin-left: 10px;\r\n  width: 95%;\r\n  // margin-top: 20px;\r\n  height: 320px;\r\n}\r\n</style>"]}]}