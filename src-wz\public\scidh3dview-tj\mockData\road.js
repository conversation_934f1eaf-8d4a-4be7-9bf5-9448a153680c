// 添加道路流光
var roadData1 = [
  // 每一项代表一条路
  [
    // 每一项代表一个点

    [26.307257506904598, 0.029999999999995978, 18.11328809786435],
    [26.298460536217036, 0.03000000000000616, -27.754678788596028],
  ],
  // 第二条
  [
    [26.720230466602864, 0.18542211535670283, -27.61641180542584],
    [26.61301807118993, 0.036671316719768754, 18.08606593346989],
  ],
  [
    [26.01301807118993, 0.036671316719768754, 18.08606593346989],
    [26.120230466602864, 0.18542211535670283, -27.61641180542584],
  ],
  [
    [26.198460536217036, 0.02000000000000616, -27.754678788596028],
    [26.207257506904598, 0.019999999999995978, 18.11328809786435],
  ],
  [
    [26.820230466602864, 0.18542211535670283, -27.61641180542584],
    [26.71301807118993, 0.036671316719768754, 18.08606593346989],
  ],
  [
    [26.17301807118993, 0.036671316719768754, 18.08606593346989],
    [26.00230466602864, 0.18542211535670283, -27.61641180542584],
  ],

  //平行道路  出发
  [
    [27.870295479289428, 0.003103061579167843, 16.802148467170337],

    [25.627224543300123, 0.003103061579167843, 16.667800625495037],
    [16.515729288481566, 0.003103061579167843, 15.289726846236364],
    [8.66288226004234, 0.003103061579167843, 13.924537488431136],
    [8.421101372217043, 0.003103061579167843, 13.779534283232437],
    [8.342672202960594, 0.003103061579167843, 13.579466095534764],
    //[8.155862974886645,  0.003103061579167843, 15.269214470979549],
    //[8.155862974886645,  0.003103061579167843, 15.269214470979549],
    [9.057506633898399, 0.003103061579167843, 4.7059452082249935],
    [9.353326921755048, 0.003103061579167843, -0.4664992950149722],
    [9.091348844884024, 0.003103061579167843, -8.294748455915284],
    [7.804544464904484, 0.003103061579167843, -16.785713043339477],
    [6.670075998168563, 0.003103061579167843, -23.8617099250413],
  ],
  [
    //出发2

    [27.815235383391105, 0.003103061579167843, 16.98010901810896],
    [25.515607748908334, 0.003103061579167843, 16.801906983819848],
    [16.482538180428513, 0.003103061579167843, 15.406523038295195],
    [8.610134139728057, 0.003103061579167843, 14.030557705893182],
    [8.289951305964891, 0.003103061579167843, 13.886461395884913],
    [8.212623072999381, 0.003103061579167843, 13.60209808546824],
    //[8.155862974886645,  0.003103061579167843, 15.269214470979549],
    //[8.155862974886645,  0.003103061579167843, 15.269214470979549],
    [8.641063323874546, 0.003103061579167843, 8.95815621474109],
    [8.962586821693273, 0.003103061579167843, 4.698011539111058],
    [9.197504069390924, 0.003103061579167843, -0.44018280563649737],
    [8.932750883259649, 0.003103061579167843, -8.388353915667409],
    [7.7516670213982515, 0.003103061579167843, -16.589628128632338],

    [6.544021730778876, 0.003103061579167843, -23.824836382710064],
  ],
  [
    //返回
    [6.38873537365952, 0.0031030615791644324, -23.766460448472863],
    [8.668851263081697, 0.003103061579167843, -8.398786562378126],
    [8.974163167445008, 0.0031030615791712533, -0.4273028116032514],
    [8.75895948870199, 0.0031030615791712533, 4.763696545243819],
    //[ 7.7642537652409445, 0.003103061579167843,  15.136829332220271]
    [7.925993253980057, 0.003103061579167843, 13.992232096290854],
    [8.132040588068582, 0.003103061579167843, 14.202932710833643],
    [8.585657539038092, 0.003103061579167843, 14.362154704486734],
    [16.575311434229896, 0.003103061579167843, 15.66938272530302],
    //[25.937237095967465, 0.25941493856377207, 17.307800706461023],
    [26.438901848912202, 0.0031030615791712533, 17.08890607806463],
    [27.83269121258058, 0.0031030615791686955, 17.17090190996282],
  ],
  [
    //返回2
    [6.4704677583421955, 0.0031030615791686955, -23.82318917140102],
    [7.668511982839014, 0.0031030615791686955, -16.305437336574624],
    [8.712659738951704, 0.0031030615791686955, -8.638032863925904],
    [9.034352184977484, 0.0031030615791686955, -0.40901849754373226],

    [8.846117071271385, 0.0031030615791686955, 4.78197334957612],
    [8.081432339908952, 0.0031030615791686955, 13.805856849655862],
    [8.1999229020582, 0.0031030615791686955, 14.052337853774118],
    [8.595902410467579, 0.0031030615791686955, 14.207122470306267],
    //[25.937237095967465, 0.25941493856377207, 17.307800706461023],
    [16.57364295474568, 0.0031030615791686955, 15.650876661812],
    [26.412367054403898, 0.0031030615791686955, 16.953017683576572],
    [27.843565935365078, 0.0031030615791686955, 17.079426362367336],
  ],
  //上方
  [
    [26.066461512244388, 0.003103061579167843, -8.377067541945884],
    [22.9652665357746, 0.003103061579167843, -7.7591687115190595],
    [19.821408433012543, 0.0031030615791661376, -7.214981926849155],
    [16.708454123042657, 0.003103061579167843, -7.011853278246839],
    [12.638355534393005, 0.003103061579167843, -7.363016431867791],
    [9.18883711421253, 0.13595621600022767, -8.110545280921853],
    [9.02118397339413, 0.15186830656197614, -8.147058577821255],
  ],
  //下
  [
    [8.750265154792759, 0.003103061579167843, -8.628034456236819],
    [13.340506086261378, 0.003103061579167843, -7.609618509560205],
    [16.712220130595945, 0.003103061579167843, -7.358006861325914],
    [20.13193417430375, 0.003103061579169548, -7.55977633551851],
    [22.964501191386766, 0.0031030615791661376, -8.06158804328046],
    [26.31499475073573, 0.003103061579167843, -8.67765809652516],
    [26.683428816533855, 0.003103061579167843, -8.807863533348751],
  ],
];

var scaleFactor = 33.3;

// 放大数据
var roadData = roadData1.map(function (road) {
  return road.map(function (point) {
    return point.map(function (coord) {
      return coord * scaleFactor;
    });
  });
});

var wallData1 = [
  [9.422617789794, 0.0010765027999878052, 13.687375134404839],
  [10.17095212558905, 0.0010765027999878052, -3.1332389946766455],
  [16.21596586669137, -0.04928234338760375, -5.1408952526661995],
  [23.66674615789477, -0.04928234338760375, -5.559275075832095],
  [23.641137456188034, 0.0010765027999878052, 13.227242610702728],
  [20.956910081642356, 0.0010765027999878052, 13.187359146983592],
  [20.622643128185544, 0.0010765027999878052, 15.599372446350138],
  [9.422617789794, 0.0010765027999878052, 13.687375134404839],
];
// var scaleFactor = 33;

// // 放大数据
// var wallData = wallData1.map(function(sublist) {
//     return sublist.map(function(value) {
//         return value * scaleFactor;
//     });
// });
