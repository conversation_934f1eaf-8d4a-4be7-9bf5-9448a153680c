<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "Electricity4",
  props: {
    usageData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chart: null,
      defaultData: [
        { name: "A区.1层.LAPYS-1", value: 580.5 },
        { name: "A区.1层.LAPYS-2", value: 520.2 },
        { name: "A区.2层.225", value: 455.8 },
        { name: "A区.2层.231", value: 398.3 },
        { name: "B区.1层.101", value: 352.6 },
        { name: "B区.1层.102", value: 315.9 },
        { name: "B区.2层.201", value: 288.4 },
        { name: "C区.1层.301", value: 252.7 },
        { name: "C区.1层.302", value: 225.1 },
        { name: "C区.2层.401", value: 198.5 },
      ],
    };
  },
  watch: {
    usageData: {
      handler(newData) {
        this.$nextTick(() => {
          if (newData && newData.length > 0) {
            this.processDataAndUpdateChart();
          } else {
            this.updateChart(this.defaultData);
          }
        });
      },
      immediate: true,
    },
  },
  methods: {
    processDataAndUpdateChart() {
      const roomStats = {};
      this.usageData.forEach((item) => {
        if (!roomStats[item.zhaddress]) {
          roomStats[item.zhaddress] = 0;
        }
        roomStats[item.zhaddress] += parseFloat(item.ylvalue || 0);
      });

      const roomData = Object.entries(roomStats)
        .map(([name, value]) => ({ name, value: parseFloat(value.toFixed(2)) }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 10);

      this.updateChart(roomData.length > 0 ? roomData : this.defaultData);
    },

    updateChart(roomData) {
      this.$nextTick(() => {
        if (!this.chart && this.$refs.chartContainer) {
          this.chart = echarts.init(this.$refs.chartContainer);
        }

        if (this.chart) {
          const option = {
            tooltip: {
              show: true,
              trigger: "item",
              backgroundColor: "rgba(10, 48, 84, 0.95)",
              borderColor: "#1e415c",
              textStyle: {
                color: "#fff",
                fontSize: 14,
              },
              formatter: function (params) {
                return `<div style="padding: 8px">
                  <div style="margin-bottom: 5px">房间：${params.name}</div>
                  <div style="color: #2cc1ff">用电量：${params.value} kwh</div>
                </div>`;
              },
            },
            grid: {
              top: "10%",
              left: "3%",
              right: "4%",
              bottom: "0%",
              containLabel: true,
            },
            xAxis: {
              type: "category",
              data: roomData.map((item) => item.name),
              axisLabel: {
                color: "#fff",
                interval: 0,
                rotate: 35,
                formatter: function (value) {
                  return value.length > 9
                    ? value.substring(0, 8) + "..."
                    : value;
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#1e415c",
                },
              },
            },
            yAxis: {
              type: "value",
              name: "用电量(kwh)",
              nameLocation: "end",
              nameTextStyle: {
                color: "#fff",
                align: "right",
                padding: [0, 0, 0, 10],
              },
              axisLabel: {
                color: "#fff",
              },
              axisLine: {
                lineStyle: {
                  color: "#1e415c",
                },
              },
              splitLine: {
                lineStyle: {
                  color: "#1e415c",
                  opacity: 0.3,
                },
              },
            },
            series: [
              {
                name: "用电量",
                type: "bar",
                barWidth: "40%",
                data: roomData.map((item) => ({
                  value: item.value,
                  name: item.name,
                  itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      { offset: 0, color: "#ffa170" },
                      { offset: 1, color: "#D35C10" },
                    ]),
                  },
                })),
                label: {
                  show: true,
                  position: "top",
                  color: "#fff",
                  formatter: "{c} kwh",
                },
              },
            ],
          };

          this.chart.setOption(option);
        }
      });
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (!this.usageData || this.usageData.length === 0) {
        this.updateChart(this.defaultData);
      }
      window.addEventListener("resize", this.handleResize);
    });
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.handleResize);
  },
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 375px;
}
</style>