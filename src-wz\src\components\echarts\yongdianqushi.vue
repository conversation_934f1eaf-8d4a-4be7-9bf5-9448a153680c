<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartData"],
  data() {
    return {};
  },
  watch: {
    echartData(newVal) {
      this.init();
      console.log(newVal);
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    init() {
      let data = this.echartData;
      console.log(data,111);
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        title: {
          text: "KWh",
          left: "18px",
          top: "2px",
          textStyle: {
            color: "#23E1FD",
            fontSize: 14,
            fontWeight: "400",
          },
        },
        color: ["#73A0FA", "#73DEB3", "#FFB761"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
            lineStyle: {
              type: "dashed",
            },
          },
        },
        grid: {
          left: "25",
          right: "25",
          bottom: "24",
          top: "40",
          containLabel: true,
        },
        legend: {
          data: [],
          orient: "horizontal",
          icon: "rect",
          show: true,
          right: 20,
          top: -1,
          textStyle: {
            fontSize: 10,
            color: "#23E1FD",
          },
        },
        xAxis: {
          type: "category",
          data:data.xAxis,
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: "#999",
            textStyle: {
              fontSize: 12,
              color: "#23E1FD",
            },
          },
        },
        yAxis: {
          type: "value",
          // max: max_value>=100? max_value + 100: max_value+10,
          // max: max_value > 100 ? max_value * 2 : max_value + 10,
          // interval: 10,
          // nameLocation: "center",
          axisLabel: {
            color: "#999",
            textStyle: {
              fontSize: 12,
              color: "#23E1FD",
            },
          },
          splitLine: {
            show: true,
            type: "dashed",
            lineStyle: {
              color: "#172A4E",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        series: [
          // {
          //   name: "空压用电",
          //   type: "line",
          //   smooth: true,
          //   data: [13, 1, 4, 44, 45, 322, 76, 13, 1, 4, 44, 45, 322, 76],
          // },
          // {
          //   name: "空调用电",
          //   type: "line",
          //   smooth: true,
          //   data: [13, 54, 34, 344, 35, 53, 13, 54, 34, 344, 35, 53],
          // },
          // {
          //   name: "氮气用电",
          //   type: "line",
          //   smooth: true,
          //   data: [13, 2, 2, 32, 123, 23, 136, 3, 2, 2, 32, 123, 23, 136],
          // },
          // {
          //   name: "真空用电",
          //   type: "line",
          //   smooth: true,
          //   data: [13, 2, 2, 32, 123, 23, 136, 13, 2, 2, 32, 123, 23, 136],
          // },
          // {
          //   name: "照明用电",
          //   type: "line",
          //   smooth: true,
          //   data: [13, 2, 2, 32, 123, 23, 136, 13, 2, 2, 32, 123, 23, 136],
          // },
          // {
          //   name: "生产用电",
          //   type: "line",
          //   smooth: true,
          //   data: [13, 2, 2, 32, 123, 23, 136, 13, 2, 2, 32, 123, 23, 136],
          // },
          // {
          //   name: "公共用电",
          //   type: "line",
          //   smooth: true,
          //   data: [13, 2, 2, 32, 123, 23, 136],
          // },
          // {
          //   name: "废排用电",
          //   type: "line",
          //   smooth: true,
          //   data: [11, 12, 12, 2, 23, 50, 36, 32, 123, 23, 136, 13, 2],
          // },
          // {
          //   name: "办公区用电",
          //   type: "line",
          //   smooth: true,
          //   data: [13, 2, 2, 32, 123, 23, 136, 32, 123, 23, 136, 13, 2],
          // },
        ],
      };
      this.echartData.yAxis.forEach(function (series) {
        option.legend.data.push(series.name);
        option.series.push({
          name: series.name,  
          type: 'line',
          smooth: true,
          data: series.data
        });
      });
      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  // margin-top: 40px;
  width: 1449px;
  height: 448px;
}

// @media (max-height: 13.5rem) {
//   .echart {
//     width: 377px;
//     height: 160px !important;
//   }
// }
</style>