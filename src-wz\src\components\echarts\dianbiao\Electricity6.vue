<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      // app.title = "未优化用电量";

      var data1 = [20, 30, 20, 30, 20, 30, 20];
      var data2 = [9, 30, 9, 60, 70, 20, 59];
      var data3 = [20, 30, 20, 30, 20, 30, 20];
      var data4 = [9, 30, 9, 60, 70, 20, 59];
      var datacity = [
        "一楼用电",
        "二楼用电",
        "三楼用电",
        "四楼用电",
        "五楼用电",
        "暖通风设备用电",
      ];
      const option = {
        title: {
          text: "kwh",
          left: "20px",
          top: "14",
          textStyle: {
            color: "#fff",
            fontSize: 14,
            fontWeight: "400",
          },
        },
        color: [
          "#66C4FC",
          "#7DFDD2",
          "#83FB45",
          "#E1FC4A",
          "#EE8B3D",
          "#E93437",
          "#EB46FB",
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
            lineStyle: {
              type: "dashed",
            },
          },
        },
        grid: {
          //图表的位置
          top: "20%",
          left: "3%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },

        legend: {
          data: [
            "一楼用电",
            "二楼用电",
            "三楼用电",
            "四楼用电",
            "五楼用电",
            "暖通风设备用电",
          ],
          top: "2%",
          right: "0px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
            fontSize: 11,
          },
        },
        xAxis: {
          axisLabel: {
            interval: 0,
            show: true,
            splitNumber: 15,
            textStyle: {
              fontSize: 10,
              color: "#fff",
            },
          },
          type: "category",
          data: [
            "1月",
            "2月",
            "3月",
            "4月",
            "5月",
            "6月",
            "7月",
            "8月",
            "9月",
            "10月",
            "11月",
            "12月 ",
          ],
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        yAxis: {
          type: "value",
          // max: max_value>=100? max_value + 100: max_value+10,
          // max: max_value > 100 ? max_value * 2 : max_value + 10,
          // interval: 10,
          // nameLocation: "center",
          axisLabel: {
            color: "#fff",
            textStyle: {
              fontSize: 12,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "#F3F4F4",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
        },
        series: [
          {
            name: "一楼用电",
            type: "line",
            smooth: true,
            data: [70, 124, 152, 171, 252, 260, 264, 336, 340, 348, 429, 448],
          },
          {
            name: "一楼用电",
            type: "line",
            smooth: true,
            data: [137, 149, 153, 166, 180, 199, 201, 399, 401, 420, 434, 447],
          },
          {
            name: "二楼用电",
            type: "line",
            smooth: true,
            data: [51, 87, 102, 137, 179, 207, 285, 315, 393, 421, 463, 498],
          },
          {
            name: "三楼用电",
            type: "line",
            smooth: true,
            data: [70, 71, 107, 210, 237, 241, 253, 347, 359, 363, 390, 493],
          },
          {
            name: "四楼用电",
            type: "line",
            smooth: true,
            data: [98, 108, 138, 219, 268, 269, 285, 315, 331, 332, 381, 462],
          },
          {
            name: "五楼用电",
            type: "line",
            smooth: true,
            data: [
              64, 224, 237, 239, 239, 239, 257, 343, 361, 361, 361, 363, 376,
              536,
            ],
          },
          {
            name: "暖通风设备用电",
            type: "line",
            smooth: true,
            data: [100, 104, 113, 157, 180, 293, 298, 302, 307, 420, 443, 487],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 593px;
  height: 200px;
}
</style>