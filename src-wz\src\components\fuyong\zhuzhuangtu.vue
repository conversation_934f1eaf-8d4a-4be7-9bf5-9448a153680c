<template>
  <div class="echart" ref="echart"></div>
</template>
    
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    chartData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);
      let data = this.chartData ? this.chartData : {
        title: ["已领用", "未领用"],
        xAxisdata: ["20", "21", "22", "23", "25", "24",],
        yAxisdata1: [4, 7, 5, 9, 6, 5],
        yAxisdata2: [4, 7, 5, 9, 6, 5],
      }
      app.title = "";

      const option = {
        //     legend: {
        //   textStyle: {
        //                 color:"#fff",
        //                 fontSize:14
        //             },
        //         top: '1%',
        //     right:"1%",
        //         data: ['未优化用电量', 'AI优化用电量'],
        //     },

        color: ["#3398DB"],
        title: {
          text: "单位：台",
          x: "6%",
          y: "2%",
          textStyle: {
            color: "#fff",
            fontSize: 16,
          },
        },
        legend: {
          itemGap: 16,
          itemWidth: 10,
          itemHeight: 10,
          top: 0,
          right: "1%",
          textStyle: { fontSize: 16, color: "#fff" },
          data: data.title,
        },
        grid: {
          top: "18%",
          bottom: "0%",
          left: "6%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: data.xAxisdata,

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
                fontSize: 16,
              },
            },
          },
        ],
        yAxis: [
          {
            axisTick: {
              alignWithLabel: true,
            },

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", 
                fontSize: 16,// 将 Y 轴标签字体颜色设置为白色
              },
            },
          },
        ],

        series: [
          {
            name: data.title[0],
            type: "bar",
            barWidth: "20%",
            data: data.yAxisdata1,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#FF9933",
                  },
                  {
                    offset: 1,
                    color: "#FF9900",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
          {
            name: data.title[1],
            type: "bar",
            barWidth: "20%",
            data: data.yAxisdata2,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#66CC33",
                  },
                  {
                    offset: 1,
                    color: "#00FF00",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
    
<style lang="less" scoped>
.echart {
  width: 450px;
  height: 330px;
}

// @media (max-height: 1080px) {
//   .echart {
//     width: 325px;
//     height: 190px;
//   }
// }
</style>