<template>
  <div class="echart">
    <div class="item">
      <div class="img">{{ (yesterdayFee / 10000).toFixed(1) }}</div>
      <div class="wenzi">昨日/万元</div>
    </div>
    <div class="item">
      <div class="img1">{{ (monthlyFee / 10000).toFixed(1) }}</div>
      <div class="wenzi">本月/万元</div>
    </div>
    <div class="item">
      <div class="img2">{{ (yearlyFee / 10000).toFixed(1) }}</div>
      <div class="wenzi">本年/万元</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    yesterdayFee: {
      type: Number,
      default: 0,
    },
    monthlyFee: {
      type: Number,
      default: 0,
    },
    yearlyFee: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 485px;
  height: 180px;
  // margin-top: 4px;
  // margin-bottom: 4px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-family: Alibaba PuHuiTi;
    font-weight: bold;
    font-size: 21px;
    color: #ffffff;
    text-align: center;
    // margin-top: 8px;
    .img {
      width: 155px;
      height: 85px;
      background: url("../../../assets/image/feiyong1.png");
      background-size: 100% 100%;
      background: no-clip;
    }
    .img1 {
      width: 155px;
      height: 85px;
      background: url("../../../assets/image/feiyong2.png");
      background-size: 100% 100%;
      background: no-clip;
    }
    .img2 {
      width: 155px;
      height: 85px;
      background: url("../../../assets/image/feiyong3.png");
      background-size: 100% 100%;
      background: no-clip;
    }
    .wenzi {
      margin-top: 19px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 19px;
      color: #e6e6e6;
    }
  }
}

// @media (max-height: 1080px) { 
//   .echart {
//     width: 667px;
//     height: 220px;
//   }
// }
</style>
