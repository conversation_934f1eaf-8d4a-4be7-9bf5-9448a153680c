<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() { },
    init() {
      const myChart = echarts.init(this.$refs.echart);



      const option = {
        //     legend: {
        //   textStyle: {
        //                 color:"#fff",
        //                 fontSize:14
        //             },
        //         top: '1%',
        //     right:"1%",
        //         data: ['未优化用电量', 'AI优化用电量'],
        //     },

        color: ["#3398DB"],
        title: {
          text: "个",
          x: "6%",
          y: "8%",
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },
        legend: {
          data: ["报警数"],
          top: "8%",
          right: "40px",
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
            fontSize:15
          },
        },
        grid: {
          top: "18%",
          bottom: "6%",
          left: "2%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: ["氧气O₂", "氮气N₂", "氦气He", "氢气H₂", "二氧化碳CO₂", "一氧化碳CO", "氨气NH₃", "氩气Ar"],

            axisLabel: {
              show: true,
              rotate: 45,  // 设置标签旋转角度为45度
              textStyle: {
                color: "#fff", // 将 X 轴标签字体颜色设置为白色
              },
            },
          },
        ],
        yAxis: [
          {
            axisTick: {
              alignWithLabel: true,
            },

            axisLabel: {
              show: true,
              textStyle: {
                color: "#fff", 
                fontSize:15
                // 将 Y 轴标签字体颜色设置为白色
              },
            },
          },
        ],

        series: [
          {
            name: "报警数",
            type: "bar",
            barWidth: "40%",
            data: [1, 2, 1, 3, 2, 1,4, 1, 0,1, 1],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#66C4FC",
                  },
                  {
                    offset: 1,
                    color: "#66C4FC",
                  },
                ]),
                shadowColor: "rgba(0, 0, 0, 0.1)",
                shadowBlur: 10,
              },
            },
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  margin-left: 10px;
  width: 95%;
  // margin-top: 20px;
  height:320px;
}
</style>