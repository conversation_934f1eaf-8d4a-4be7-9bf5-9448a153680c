<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>three sdk</title>

    <style>
      html,
      body {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
      }
      .divtit1 {
        margin-top: -5.2px;
      }
      .container {
        width: 100%;
        height: 100%;
      }
      .icon1 {
        margin-top: 1px;
        margin-left: 2px;
        width: 3px;
        height: 3px;
        background-color: aqua;
        border-radius: 50%;
      }
      .icon {
        height: 4px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
      }
      #myList1 {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 10;
        display: none;
      }
      #myList2 {
        position: fixed;
        left: 50px;
        top: 0;
        z-index: 10;
        display: none;
      }
      ul {
        font-size: 12px;
        list-style-type: none;
        padding: 0px 2px;
        text-align: center;
      }
      li {
        padding: 6px;
        background-color: #f0f0f0;
        margin-bottom: 5px;
        cursor: pointer;
      }
      /* li:hover {
        background-color: #ccc;
      } */
      li.selected {
        background-color: rgb(97, 94, 94);
      }
      .loading_page {
        position: fixed;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: 11199999;
        display: flex;
        flex-direction: column;
        /* Stack items vertically */
        justify-content: center;
        /* Center items vertically */
        align-items: center;
        /* Center items horizontally */

        background-color: rgb(33, 33, 33);
        margin: 0;
      }
      .inner-box {
        margin-left: 32.5px;
        position: relative;
        width: 36px;
        height: 36px;
        transform-style: preserve-3d;
        transform-origin: center;
        animation: 3s ctn infinite;
        transform-origin: 0 0;
        transform: rotateX(-30deg) rotateY(45deg) translate(0, 0);
      }

      .inner {
        position: absolute;
        width: 36px;
        height: 36px;
        text-align: center;
        line-height: 36px;
        color: #fff;
        border-radius: 6px;
        background: rgba(7, 127, 240, 0.1);
        border: 2px solid rgba(19, 108, 241, 0.986);
        transform-origin: center;
      }

      .inner:nth-child(1) {
        transform: rotateX(90deg) translateZ(18px);
        animation: 3s top infinite;
      }

      .inner:nth-child(2) {
        transform: rotateX(-90deg) translateZ(18px);
        animation: 3s bottom infinite;
      }

      .inner:nth-child(3) {
        transform: rotateY(90deg) translateZ(18px);
        animation: 3s left infinite;
      }

      .inner:nth-child(4) {
        transform: rotateY(-90deg) translateZ(18px);
        animation: 3s right infinite;
      }

      .inner:nth-child(5) {
        transform: translateZ(18px);
        animation: 3s front infinite;
      }

      .inner:nth-child(6) {
        transform: rotateY(180deg) translateZ(18px);
        animation: 3s back infinite;
      }

      @keyframes ctn {
        from {
          transform: rotateX(-35deg) rotateY(45deg) translate(-50%, -50%);
        }

        50% {
          transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
        }

        to {
          transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
        }
      }

      @keyframes top {
        from {
          transform: rotateX(90deg) translateZ(18px);
        }

        50% {
          transform: rotateX(90deg) translateZ(18px);
        }

        75% {
          transform: rotateX(90deg) translateZ(36px);
        }

        to {
          transform: rotateX(90deg) translateZ(18px);
        }
      }

      @keyframes bottom {
        from {
          transform: rotateX(-90deg) translateZ(18px);
        }

        50% {
          transform: rotateX(-90deg) translateZ(18px);
        }

        75% {
          transform: rotateX(-90deg) translateZ(36px);
        }

        to {
          transform: rotateX(-90deg) translateZ(18px);
        }
      }

      @keyframes left {
        from {
          transform: rotateY(90deg) translateZ(18px);
        }

        50% {
          transform: rotateY(90deg) translateZ(18px);
        }

        75% {
          transform: rotateY(90deg) translateZ(36px);
        }

        to {
          transform: rotateY(90deg) translateZ(18px);
        }
      }

      @keyframes right {
        from {
          transform: rotateY(-90deg) translateZ(18px);
        }

        50% {
          transform: rotateY(-90deg) translateZ(18px);
        }

        75% {
          transform: rotateY(-90deg) translateZ(36px);
        }

        to {
          transform: rotateY(-90deg) translateZ(18px);
        }
      }

      @keyframes front {
        from {
          transform: translateZ(18px);
        }

        50% {
          transform: translateZ(18px);
        }

        75% {
          transform: translateZ(36px);
        }

        to {
          transform: translateZ(18px);
        }
      }

      @keyframes back {
        from {
          transform: rotateY(180deg) translateZ(18px);
        }

        50% {
          transform: rotateY(180deg) translateZ(18px);
        }

        75% {
          transform: rotateY(180deg) translateZ(36px);
        }

        to {
          transform: rotateY(180deg) translateZ(18px);
        }
      }

      .loading-text {
        z-index: 9999;
        color: #fff;
        /* Text color */
        margin-top: 25px;
        /* Space between the cube and text */
        font-size: 16px;
        /* Text size */
        letter-spacing: 1px;
        /* Letter spacing */
        text-align: center;
      }

      /* Reset default margin */
    </style>
  </head>
  <body>
    <div id="loading-page" class="loading_page">
      <div class="inner-box">
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="inner"></div>
        <div class="inner"></div>
      </div>
      <div class="loading-text">正在加载中,请耐心等候</div>
      <!-- 添加文本 -->
    </div>
    <ul id="myList1">
      <li data-name="1">B1栋</li>
      <li data-name="2">B2栋</li>
      <li data-name="3">B3栋</li>
      <li data-name="4">B4栋</li>
      <li data-name="5">W1栋</li>
      <li data-name="6">W2栋</li>
    </ul>
    <ul id="myList2">
      <!-- <li data-name="配电系统">配电系统</li>
      <li data-name="冰机房中温">冰机房中温</li>
      <li data-name="冰机房低温">冰机房低温</li>
      <li data-name="工艺冷却水">工艺冷却水</li>
      <li data-name="空调箱系统">空调箱系统</li>
      <li data-name="空压系统">空压系统</li>
      <li data-name="真空系统">真空系统</li>
      <li data-name="制氮系统">制氮系统</li>
      <li data-name="废排系统">废排系统</li>
      <li data-name="热水系统">热水系统</li>
      <li data-name="配电房">配电房</li>
      <li data-name="自来水系统">自来水系统</li> -->
    </ul>

    <div class="container" id="container"></div>

    <script src="./build/sdk.js"></script>
    <script src="./mockData/road.js"></script>
    <script src="./mockData/config.js"></script>
    <script src="./js/xz.js"></script>
    <script src="./js/lable.js"></script>
    <script src="./js/msg-execute3d.js"></script>
    <script src="./js/json.js"></script>
    <script src="./js/yanwu.js"></script>
    <script src="./js/modelData.js"></script>
    <script src="./js/axios.min.js"></script>
    <script>
      //更新地址栏

      function updateUrlParameter(key, value) {
        var url = new URL(window.location.href);
        url.searchParams.set(key, value);
        window.history.replaceState({}, "", url);
      }
      var buildings = [];
      var myList2 = document.getElementById("myList2");
      function updateList2(selectedName) {
        // 清空第二个列表
        myList2.innerHTML = "";

        // 根据第一个列表中选定的项来动态更新第二个列表
        var index = parseInt(selectedName) - 1; // 将选定的项转换为索引
        var items = buildings[index];
        items.forEach(function (item) {
          var li = document.createElement("li");
          li.textContent = item;
          li.setAttribute("data-name", item); // 设置 data-name 属性
          myList2.appendChild(li);
        });
      }
      //分组名
      var layerMap = [];
      //对应的系统管道名
      var lineMap = [];
      let maplist;
      var listItems1 = document.querySelectorAll("#myList1 li");
      listItems1.forEach(function (item) {
        item.addEventListener("click", function () {
          let selectedName = this.getAttribute("data-name");
          console.log(selectedName);
          listItems1.forEach(function (li) {
            li.classList.remove("selected");
          });
          this.classList.add("selected");
          updateUrlParameter("id", selectedName);
          // location.reload();

          // 在这里可以将点击的结果保存到任何你想要的地方
        });
      });

      // 将点击事件绑定到第二个列表的父元素上，并使用事件委托来处理点击事件
      document
        .getElementById("myList2")
        .addEventListener("click", function (event) {
          if (event.target.tagName === "LI") {
            var selectedName = event.target.getAttribute("data-name");
            console.log(selectedName);
            var listItems = document.querySelectorAll("#myList2 li");
            listItems.forEach(function (li) {
              li.classList.remove("selected");
            });
            event.target.classList.add("selected");
            // var layers = maplist[selectedName];
            // if (layers) {
            //   view.setLayer(layers);
            // }
            // 在这里可以将点击的结果保存到任何你想要的地方
          }
        });

      //动态的场景json数据
      let pos;
      let tar;
      let path;
      let uid;
      const urlid = new URL(location.href).searchParams.get("id");

      if (urlid) {
        uid = urlid;
        // uid = urlid;
        //有id
        console.log("有id");
        console.log(uid);
      } else {
        uid = 7;
        //没有id
        console.log("没有id");
      }

      console.log(uid);

      maplist = layerMap[uid - 1];
      linemaplist = lineMap[uid - 1];
      // pos = modeljson[uid - 1].position;
      // tar = modeljson[uid - 1].target;
      path = "./models/jzdm3_noyinying_blue.glb";
      const view = new app3d({
        dom: "container",
        dracoPath: "./build/draco/gltf/",
      });
      let resultArray12 = [];
      for (let i = 1; i <= 12; i++) {
        let result = `b3_rsxt_flrb_${i.toString().padStart(3, "0")}`;
        resultArray12.push(result);
      }
      let resultArray22 = [];
      for (let i = 1; i <= 10; i++) {
        let result = `dw_shuibeng_${i.toString().padStart(3, "0")}`;
        resultArray22.push(result);
      }
      let resultArray32 = [];
      for (let i = 1; i <= 12; i++) {
        let result = `zw_shuibeng_${i.toString().padStart(3, "0")}`;
        resultArray32.push(result);
      }
      const config = {
        lightConfig: [
          {
            type: "AmbientLight",
            color: "#aaaaff",
            intensity: 1,
          },
          {
            type: "PointLight",
            color: "#8888aa",
            intensity: 20125,
            distance: 5025,
            position: [
              543.6780301790445, 100.035883426666231344, 509.8497462750016,
            ],
          },
          {
            type: "PointLight",
            color: "#ccccff",
            intensity: 21345,
            distance: 5125,
            position: [
              827.7396957261084, 90.6427447795867778, 367.52472001588774,
            ],
          },
          {
            type: "PointLight",
            color: "#ffffff",
            intensity: 42040,
            distance: 5425,
            position: [
              300.04655679873764, 190.517336277997892, 200.87465861420694,
            ],
          },
        ],
        dracoPath: "./build/draco/gltf/",
        hdrPath: "./textures/equirectangular/moonless_golf_1k.hdr",
        camera: {
          position: [50.287936743517896, 102.693360025209, 7.907777150690692],

          target: [-45.25702668260484, 2.433761874895441, 9.512124193183674],
          // position: pos,
          // target: tar,
          near: 1, // 近截面
          far: 3000000,
        },
        css2d: {
          use: true,
        },
        css3d: {
          use: true,
        },
        useEffectComposer: true,
        models: [
          {
            path: path,
            position: [0, 0, 0],
            name: "shebei",
            scale: [1, 1, 1],
            rotation: [0, 0, 0],
            id: 1,
            visible: true,
            groupNames: [
              // "B1",
              // "B2",
              // "B3",
              // "B4",
              // "W1",
              // "W2",
              "C1",
              "C2",
              "sushe_01",
              "sushe_02",
              "sushe_03",
              "sushe_04",
              "sushe_05",
              "sushe_06",
              "sushe_07",
              "sushe_08",
              "sushe_09",
              "sushe_10",
            ], // 用来配置点击选中是可以选择个组，例如一栋楼，一个电机。而不是只能选中某一部件
            // .concat(resultArray12)
            // .concat(resultArray22)
            // .concat(resultArray32)
            isGlow: true, // 是否使用辉光
            glowNames: [
              "B1",
              "B2",
              "B3",
              "B4",
              "W1",
              "W2",
              "C1",
              "C2",
              "sushe_01",
              "sushe_02",
              "sushe_03",
              "sushe_04",
              "sushe_05",
              "sushe_06",
              "sushe_07",
              "sushe_08",
              "sushe_09",
              "sushe_10",
              "flrb_01",
              "flrb_02",
              "flrb_03",
              "flrb_04",
              "flrb_05",
              "flrb_06",
              "flrb_07",
              "flrb_08",
              "flrb_09",
              "flrb_10",
              "flrb_11",
              "flrb_12",
            ], //如果设置就是局部辉光，如果不设置就是整体辉光
            transparentConf: [
              //配置哪些组需要设置透明度
              {
                names: [
                  // "B1",
                  // "B2",
                  // "b31f",
                  // "b32f",
                  // "b33f",
                  // "b34f",
                  // "b3_ding",
                  // "b3ding_wd",
                  // "B4",
                  // "W1",
                  // "W2",
                  // "C1",
                  // "C2",
                ],
                opacity: 0.38,
              },
            ],
            // {
            //   names: ["rf_sb"],
            //   opacity: 1,
            // },

            // industryNames: ["B3"],
          },
        ],
      };

      // 初始话场景
      view.init(config);
      view.setBloomParams({
        threshold: 0.3,
        strength: 0.35,
        radius: 0.2,
      });
      // 添加天空盒
      // view.setSkyBox(["./textures/sky1/panoright.jpg","./textures/sky1/panoleft.jpg",  "./textures/sky1/panotop.jpg", "./textures/sky1/panobottom.jpg", "./textures/sky1/panofront.jpg", "./textures/sky1/panoback.jpg"]);
      view.setSkyBox([
        "./textures/yewan/panoright.jpg",
        "./textures/yewan/panoleft.jpg",
        "./textures/yewan/panotop.jpg",
        "./textures/yewan/panobottom.jpg",
        "./textures/yewan/panofront.jpg",
        "./textures/yewan/panoback.jpg",
      ]);
      view.setNotAllowSelect([
        "Line2029251257",
        "mmm7948gg",
        "Line2029251149",
        "Shape027",
        "qqagw",
        "pCube8",
        "tikuai.001",
      ]);

      // view.addWall(wallData, "./textures/wall.png", 0.43);

      view.setCallBack({
        mousemove: mousemove,
        mouseclick: mouseclick,
        progress: progress,
        mouseDbClick: mouseDbClick,
      });
      view.needDoubleClickSetLayer(true);
      let deviceLabels = {};
      autoRoate = function (flag, speed) {
        view.controls.autoRotate = flag;
        view.controls.autoRotateSpeed = speed;
      };
      // 设置一个变量来存储 setTimeout 的返回值
      // 设置一个变量来存储 setTimeout 的返回值
      let timeoutId = null;

      // 这是您想要在鼠标10秒内没有任何操作时执行的函数
      function onInactive() {
        //console.log("鼠标已经10秒没有任何操作");
        // 在这里执行您的逻辑
        recordxz();
        autoRoate(true, 1); // 假设这是一个示例函数，根据您的需要调整
      }

      // 这是您想要在鼠标有操作时执行的函数
      function onActive() {
        console.log("鼠标有操作");
        // 在这里执行您的逻辑
        autoRoate(false, 3); // 假设这是一个示例函数，根据您的需要调整
      }
      const recode = 60; //未操作页面开始旋转时间 单位s
      // 假设deviceLabels已经在外部定义，用于跟踪所有已创建的标签
      function resetTimer() {
        // 首先，调用活动函数
        onActive(); // 每次用户有操作时调用

        // 如果已经有一个计时器在运行，则先清除它
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        // 设置一个新的计时器
        timeoutId = setTimeout(onInactive, recode * 1000); // 10000ms = 10s
      }
      // view.addDirectionalLight({
      //   color: "#fff",
      //   intensity: 1.2,
      //   position: [25.48246372423977, 0.958647042239285, -23.898312155269824],
      // });
      function mousedown() {
        console.log(111);
        // resetTimer();
      }
      function mousemove(obj) {
        const { model, point, center } = obj;
        view.setOutlineModel([model]);
      }

      function progress(load, isload) {
        view.setSelectWholeGroup([]);

        // 设置那些对象可以选中

        view.setAllowSelect([]); // 不设置默认不可选中

        //上下翻转的最大角度
        view.controls.maxPolarAngle = 1.5;
        //上下翻转的最小角度
        view.controls.minPolarAngle = 0.3;
        console.log("progress:", load);
        if (isload) {
          window.parent.postMessage(
            {
              type: "finished",
            },
            "*"
          );
          console.log(
            view.getObjCenterByNames([
              "P-WD01(M)",
              "P-WD01(N)",
              "P-WD01(S)",
              "P-WD010(N)",
              "P-WD010(S)",
              "P-WD02(M)",
              "P-WD02(N)",
              "P-WD02(S)",
              "P-WD03(M)",
              "P-WD03(N)",
              "P-WD03(S)",
              "P-WD04(M)",
              "P-WD04(N)",
              "P-WD04(S)",
              "P-WD05(M)",
              "P-WD05(N)",
              "P-WD05(S)",
              "P-WD06(M)",
              "P-WD06(N)",
              "P-WD06(S)",
              "P-WD07(M)",
              "P-WD07(N)",
              "P-WD07(S)",
              "P-WD08(N)",
              "P-WD08(S)",
              "P-WD09(N)",
              "P-WD09(S)",
            ]),
            "zzd"
          );

          // addlableldbq(
          //   view.getObjCenterByNames([
          //     "B1",
          //     "B2",
          //     "B3",
          //     "B4",
          //     "W1",
          //     "W2",
          //     "C1",
          //     "C2",
          //     "sushe_01",
          //     "sushe_02",
          //     "sushe_03",
          //     "sushe_04",
          //     "sushe_05",
          //     "sushe_06",
          //     "sushe_07",
          //     "sushe_08",
          //     "sushe_09",
          //     "sushe_10",
          //   ])
          // );

          view.toggleShowStyle(true);
          view.setOpa(["floor"], 0.08, "#0066FF");

          view.clearAllLight(); // 清除所有灯光
          let lightConfig = [
            {
              type: "AmbientLight",
              color: "#aaaaff",
              intensity: 1,
            },
            {
              intensity: 1.5,
              type: "DirectionalLight",
              color: "#fff",
              position: [
                353.1440322709692, 32.118162337619367, 415.14587542705004,
              ],
            },
          ];
          view.setLight(lightConfig);
          const data = [
            {
              path: "lineglb/guandao(1).glb",
              id: 1,
            },
          ];
          /**
           * 根据模型获取管道数据
           * @新接口
           */
          view.DrawLine.getTubePointsByModel(data, (points) => {
            console.log("===points===", points);
            linenames = Object.keys(points[1]);
            console.log(linenames, 111);

            const data = points[1];

            let tubeData = [];
            // console.log(data, "管道信息");
            for (let key in data) {
              let points = data[key];
              tubeData.push({
                useShader: true,
                color: "#FFFF77",
                bgColor: key.includes("fense")
                  ? "pink"
                  : key.includes("huangse")
                  ? "beige"
                  : key.includes("hongse")
                  ? "red"
                  : key.includes("huise")
                  ? "gray"
                  : key.includes("zise")
                  ? "purple"
                  : key.includes("lvse")
                  ? "green"
                  : key.includes("qingse")
                  ? "cyan"
                  : key.includes("chengse")
                  ? "orange"
                  : key.includes("qianlanse")
                  ? "skyBlue"
                  : key.includes("juse")
                  ? "orange"
                  : key.includes("qianfense")
                  ? "LightPink"
                  : "#1AE5EF",
                name: key,
                points: points,
                direction: 1,
                repeatX: 150,
                size: 0.12,
                isFlyline: true,
                range: 110, // 飞线长度
              });
            }
            view.DrawLine1.clearPoints();
            // view.DrawLine.setTubeData(tubeData);

            view.DrawLine1.setTubeData(tubeData, () => {
              //可以在这里关闭loadding
              console.log("====管道加载完毕=====");
              document.getElementById("loading-page").style.display = "none";
            });
          });
          // view.clearAllLight(); // 清除所有灯光
          // let lightConfig = [
          //   {
          //     type: "AmbientLight",
          //     color: "#aaaaff",
          //     intensity: 1,
          //   },
          //   {
          //     intensity: 6,
          //     type: "DirectionalLight",
          //     color: "#ffffff",
          //     position: [30, 190, 20],
          //   },
          // ];
          // view.setLight(lightConfig);

          // // addLabel();
          // //设置科技感
          // view.toggleShowStyle(true);

          // view.setLight(lightConfig);
          //  view.setLayer(["B3"]);
          // 开启单机显示当前模型轮廓
          view.needClickSetObjOutline(true);
          // 设置一组模型为一个整体被选中
          view.setSelectWholeGroup([
            "P-WD01(M)",
            "P-WD01(N)",
            "P-WD01(S)",
            "P-WD010(N)",
            "P-WD010(S)",
            "P-WD02(M)",
            "P-WD02(N)",
            "P-WD02(S)",
            "P-WD03(M)",
            "P-WD03(N)",
            "P-WD03(S)",
            "P-WD04(M)",
            "P-WD04(N)",
            "P-WD04(S)",
            "P-WD05(M)",
            "P-WD05(N)",
            "P-WD05(S)",
            "P-WD06(M)",
            "P-WD06(N)",
            "P-WD06(S)",
            "P-WD07(M)",
            "P-WD07(N)",
            "P-WD07(S)",
            "P-WD08(N)",
            "P-WD08(S)",
            "P-WD09(N)",
            "P-WD09(S)",
          ]);

          //设置默认不可选中
          // view.setNotAllowSelect(["B3"]);
          // 开启关闭mousemove事件
          // view.toggleMousemove(true);
          view.toggleMousemove(false);
          // view.setintersectArr(["B1", "B2", "B3"]);
          // 根据json添加模型
          // view.LoadJsonDevice.setScene(sceneConfig1, (obj) => {
          //   console.log(obj, 222);
          // });
          // view.LoadJsonDevice.setScene(sceneConfig2, (obj) => {
          //   console.log(obj, 222);
          // });
          // view.LoadJsonDevice.setScene(sceneConfig3, (obj) => {
          //   console.log(obj, 222);
          // });
          // view.LoadJsonDevice.setScene(sceneConfig4, (obj) => {
          //   console.log(obj, 222);
          // });

          // view.add2d(earthMassDiv, {
          //   position: {
          //     x: 22.08519033193588,
          //     y: 1.1726030545130566,
          //     z: 6.487071424478986,
          //   },
          //   name: "pupop",
          // });

          // 初始化模型风扇数据，传入风扇和出风效果模型name数据， 以及初始的风扇状态数据
          const fanNameArrs = Object.keys(modelData);
          const rotateZArr = [];
          const rotateXArr = [];
          let statusMap = {
            b3_kyz_lqtfs_001: "normal",
            b3_kyz_lqtfs_002: "normal",
            b3_kyz_lqtfs_003: "normal",
            b3_kyz_lqtfs_004: "normal",
            b3_kyz_lqtfs_005: "normal",
            b1_zw_lqtfs_001: "normal",
            b1_zw_lqtfs_002: "normal",
            b1_zw_lqtfs_003: "normal",
            b1_zw_lqtfs_004: "normal",
            b1_zw_lqtfs_005: "normal",
            b1_zw_lqtfs_006: "normal",
            b1_zw_lqtfs_007: "normal",
            b1_dw_lqtfs_001: "normal",
            b1_dw_lqtfs_002: "normal",
            b1_dw_lqtfs_003: "normal",
          };
          fanNameArrs.forEach((item) => {
            statusMap[item] = "normal"; // 默认都设置为正常
            console.log();
          });

          view.Fan.initFan(fanNameArrs, statusMap, modelData);
          // view.setOutlineModel(Object.keys(statusMap));
          view.Fan.setRotateZArr(Object.keys(statusMap));
        }
      }
      function mouseDbClick(name, model) {
        console.log(name);
        // view.setLayer([name]);

        //  if (name == "B3") {

        //   view.animateCamera(
        //     {
        //       x: 27.031664757887466,
        //       y: 5.762609290224179,
        //       z: 13.589114807899897,
        //     },
        //     {
        //       x: 19.378543379471612,
        //       y: -0.7216638762820583,
        //       z: 6.965869375288216,
        //     },
        //     0
        //   );
        // } else {
        //   view.nameTocam(name);
        // }
        // view.nameTocam(name);
      }
      function mouseclick(obj) {
        const { model, point, center } = obj;
        console.log("point:", point);
        console.log("point:", [point.x, point.y, point.z]);
        console.log("点击模型的名字：", model);

        if (
          model.name.includes("P-WD") ||
          model.name.includes("flrb") ||
          model.name.includes("dwsb") ||
          model.name.includes("zwsb")
        ) {
          view.setOutlineModel([model]);
          addlableldbq(view.getObjCenterByNames([model.name]));
        }
        console.log(obj);
        console.log(view.camera.position, view.controls.target);
        // resetTimer();
        addlable(view.getObjCenterByNames([model.name])); // 可以传多个， 模型数组
      }

      // 你需要替换view.add3dSprite和view.nameVisible方法调用以适应你的3D视图库API。
      // 同样的，确保deviceLabels对象在函数外部正确初始化，并且你的视图库提供了相应的方法来控制3D精灵的可见性。
      window.onload = function () {
        // 禁用文本选择
        document.addEventListener("selectstart", function (e) {
          e.preventDefault();
        });

        // 禁用复制
        document.addEventListener("copy", function (e) {
          e.preventDefault();
        });
      };
      document.addEventListener("contextmenu", function (event) {
        // 取消右击事件的默认行为
        event.preventDefault();
      });
    </script>
    <!-- <script src="./js/center.js"></script> -->
    <!-- <script src="./js/line.js"></script> -->
  </body>
</html>
