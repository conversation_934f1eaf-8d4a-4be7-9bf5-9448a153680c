<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {
      dates: [], // 用于存储格式化后的日期数据
      data1: [], // 最低温度数组
      data2: [], // 最高温度数组
    };
  },

  mounted() {
    this.initData();
  },

  methods: {
    initData() {
      const apiKey = "350964cec5484292ab17296f5b3d5a42"; // 替换为你的API Key
      const location = "101010100"; // 替换为需要的城市ID
      const url = `https://devapi.qweather.com/v7/weather/7d?location=${location}&key=${apiKey}`;

      fetch(url)
        .then((response) => response.json())
        .then((data) => {
          if (data && data.daily) {
            data.daily.forEach((day) => {
              // 格式化日期，仅显示月份和日期部分
              const formattedDate = day.fxDate.slice(5).replace("-", ".") ; // 将 '2024-08-30' 转为 '08月30日'
              this.dates.push(formattedDate);
              this.data1.push(day.tempMin);
              this.data2.push(day.tempMax);
            });
            // 数据准备好后，初始化图表
            this.init();
          } else {
            alert("获取天气信息失败");
          }
        })
        .catch((error) => {
          console.error("Error fetching weather data:", error);
          alert("请求失败，请检查网络或API配置");
        });
    },

    init() {
      const myChart = echarts.init(this.$refs.echart);

      const option = {
        title: {
          text: "（℃）",
          x: "3%",
          y: "0%",
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
        },
        legend: {
          data: ["最低温度", "最高温度"],
          textStyle: {
            color: "#fff",
            fontSize: 14,
          },
        },
        tooltip: {
          show: true,
          trigger: "axis",
          formatter: "{b0}: {c0}℃",
        },
        grid: {
          top: "14%",
          bottom: "16%",
          left: "5%",
          right: "6%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 12,
                color: "#fff",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#939ab6",
                opacity: 0.15,
              },
            },
            data: this.dates, // 使用格式化后的日期数据
          },
        ],
        yAxis: [
          {
            type: "value",
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 12,
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "最低温度",
            type: "line",
            showSymbol: false,
            smooth: true,
            lineStyle: {
              color: "rgba(59,102,246,1)",
              width: 2,
            },
            data: this.data1, // 使用从API获取的最低温度数据
          },
          {
            name: "最高温度",
            type: "line",
            showSymbol: false,
            smooth: true,
            lineStyle: {
              color: "rgba(118,237,252,1)",
              width: 2,
            },
            data: this.data2, // 使用从API获取的最高温度数据
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 212px;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 190px !important;
  }
}
</style>
