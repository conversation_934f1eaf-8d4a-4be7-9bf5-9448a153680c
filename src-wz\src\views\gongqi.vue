<template>
  <div>
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd" v-if="isshowwhat"></component>
    <div class="botbtn">
      <div v-for="(item, index) in changeTitle" :key="index" :class="titactive == index ? 'btt1' : 'btt'"
        @click="changetit(index)">
        {{ item }}
      </div>
    </div>
    <tedai :ids="ids" :selectedItem="selectedItem" class="sbdetails" :zengtiimg="zengtiimg" v-if="false"
      @hidedetails="hidedetailsss"></tedai>
    <biaoGesss v-if="isshow" @hidedetails="hidedetails" :tableTitle="tableTitle" :tableDataItem="tableDataItem">
    </biaoGesss>
    <div class="container" v-if="!isshowwhat">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title2 class="ltitle1" tit="环境监管">
          <div class="box">
            <div>
              <el-input class="el-input" v-model="input" placeholder="请输入内容"></el-input>
            </div>
            <!-- <div class="xiaobox">
              <img class="siqiu" src="../assets/image/shixinqiu.png" alt="" />
              <div class="shuru">全部设备</div>
            </div> -->
            <div class="menu">
              <div v-for="(menu, index) in menus" :key="index" class="menu-group">
                <div :style="{ color: activeSubmenu == menu.id ? '#00ffc0' : '' }" class="menu-item"
                  @click="toggleSubMenu(menu.id, menu.title, index)">
                  {{ menu.title }}
                </div>
                <div v-show="activeSubmenu === menu.id" class="submenu">
                  <div v-for="(item, subIndex) in menu.submenu" :style="{
                    color: activeSubSubmenu === item.id ? '#00ffc0' : '',
                  }" :key="subIndex" class="submenu-items">
                    <div class="bq" @click="toggleSubSubMenu(item.id, item.title, index)">
                      {{ item.title }}
                    </div>
                    <div v-show="activeSubSubmenu === item.id" class="submenu">
                      <div v-for="(subItem, thirdIndex) in item.submenu" :key="thirdIndex" :style="{
                        color: selectedIndex == thirdIndex ? '#00ffc0' : '',
                      }" class="submenu-item" @click="setContent(subItem, thirdIndex)">
                        {{ subItem.title }}
                        <!-- <div class="listtype">使用中</div> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- shebei12.png -->
        </Title2>
      </div>
      <!-- shebeibgc.png -->
      <!-- 右侧内容 -->
      <!-- 右侧内容 -->

      <div v-show="detalis && detalis.length" class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title3 :tit="cgqname">
          <div class="box">
            <div class="xiaoboxs" v-for="item in detalis" :key="item">
              <img class="siqiu" src="../assets/image/kongxin.png" alt="" />
              <div class="nihaowo">{{ item.name }}</div>
              <div class="shuru">{{ item.value }}</div>
            </div>
          </div>
          <hr class="hr" />
          <echarts2 ref="echarts2" :chart-data="chartData" :unit="dataUnit" :title="cgqname" style="width: 100%">
          </echarts2>
          <hr class="hr" />
          <p class="local">
            功能介绍：<br />
            &nbsp;&nbsp;&nbsp;{{ yccontent }}
          </p>
          <hr class="hr" />
          <!-- <div class="local">
            <div style="display: flex">
              <div>维护人：</div>
              <div>王工</div>
            </div>
            <div style="display: flex; margin-top: 10px">
              <div>联系方式：</div>
              <div>173****5896</div>
            </div>
          </div> -->
        </Title3>
      </div>
    </div>
    <!-- <DeviceChart
      v-if="chartData.length"
      :chart-data="chartData"
      :unit="dataUnit"
      :title="cgqname"
    /> -->
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import component0 from "@/views/tongji/gongqi.vue";
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import tedai from "@/components/common/tedais.vue";
import biaoGesss from "@/components/common/biaoGesss.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import echarts2 from "@/components/echarts/bingjifang/echarts4.vue";
import axios from "axios";
import { getToken } from "@/utils/auth";
import DeviceChart from "@/components/echarts/DeviceChart.vue";
import { getDeviceData, getDevicedetails } from "@/api/device";

export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    component0,
    tedai,
    echarts2,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    shuangxiang,
    biaoGesss,
    component0,
    DeviceChart,
  },
  props: ["tabledata", "zengtiimg"],
  //  cgqlistss: [
  //       { name: "湿度范围：", value: "5%RH~100%RH（无结露）" },
  //       { name: "湿度分辨率：", value: "0.04%" },
  //       { name: "IP地址：", value: "±3%RH（20%RH~80%RH）" },
  //       { name: "湿度精度：", value: "-40℃~+125℃" },
  //       { name: "温度分辨率：", value: "0.01℃" },
  //       { name: "温度精度：", value: "±0.5℃（10℃~60℃）" },
  //     ],

  data() {
    // 这里存放数据
    return {
      isshowwhat: true,
      isshowsss: false,
      titactive: 0,
      changeTitle: ["数据统计", "数据列表"],
      isshowsss: false,
      activeyj: null, // 当前激活的一级菜单
      activeSubmenu: null, // 当前激活的二级菜单
      activeSubSubmenu: null, // 当前激活的三级菜单
      activeContent: null, // 当前显示的内容
      isshow: false,
      selectedIndex: null,
      selectedItem: null,
      xxxx: false,
      cgqlist: [
        { name: "温度", value: "20℃" },
        { name: "湿度", value: "12" },
        // { name: "品牌型号：", value: "海康/DS-2CD7205E-SH" },
        // { name: "IP地址：", value: "************" },
        // { name: "设备类型：", value: "高清半球型摄像机" },
      ],
      yccontent: "",
      menus: [
        {
          "id": "menu5",
          "title": "气瓶间",
          "submenu": [
            {
              "id": "submenu5-2F",
              "title": "2F",
              "submenu": [
                {
                  "title": "C218-二氧化碳传感器",
                  "content": "C218-二氧化碳传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019408",
                  "id": 546892
                }
              ]
            },
            {
              "id": "submenu5-1F",
              "title": "1F",
              "submenu": [
                {
                  "title": "C102-氧气传感器",
                  "content": "C102-氧气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019403",
                  "id": 546882
                },
                {
                  "title": "C115-氧气传感器",
                  "content": "C115-氧气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019404",
                  "id": 546883
                },
                {
                  "title": "C116-氨气传感器",
                  "content": "C116-氨气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019602",
                  "id": 546884
                },
                {
                  "title": "C116-氢气传感器",
                  "content": "C116-氢气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019603",
                  "id": 546885
                },
                {
                  "title": "C116-甲烷传感器",
                  "content": "C116-甲烷传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019604",
                  "id": 546886
                },
                {
                  "title": "E125-氧气传感器",
                  "content": "E125-氧气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019405",
                  "id": 546887
                },
                {
                  "title": "A104-氧气传感器",
                  "content": "A104-氧气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019406",
                  "id": 546888
                },
                {
                  "title": "A104-氨气传感器",
                  "content": "A104-氨气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019605",
                  "id": 546889
                },
                {
                  "title": "A104-氢气传感器",
                  "content": "A104-氢气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019606",
                  "id": 546890
                },
                {
                  "title": "A104-二氧化碳传感器",
                  "content": "A104-二氧化碳传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019407",
                  "id": 546891
                }
              ]
            },
            {
              "id": "submenu5-B1F",
              "title": "B1F",
              "submenu": [
                {
                  "title": "B132-氢气传感器",
                  "content": "B132-氢气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019601",
                  "id": 546879
                },
                {
                  "title": "B132-二氧化碳传感器",
                  "content": "B132-二氧化碳传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019401",
                  "id": 546880
                },
                {
                  "title": "B132-氧气传感器",
                  "content": "B132-氧气传感器",
                  "cgqlist": [
                    {
                      "name": "温度",
                      "value": "20℃"
                    },
                    {
                      "name": "湿度",
                      "value": "15%RH"
                    }
                  ],
                  "deviceid": "1019402",
                  "id": 546881
                }
              ]
            }
          ]
        }
      ],
      data: [
        {
          category: "仪器设备",
          items: [
            {
              number: "LAB001",
              nanme: "显微镜",
              pingpai: "品牌X",
              baozhuang: "3楼",
              xiaobaozhuang: "A305",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "离心机",
              pingpai: "品牌Y",
              baozhuang: "2楼",
              xiaobaozhuang: "B210",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "培养箱",
              pingpai: "品牌Z",
              baozhuang: "3楼",
              xiaobaozhuang: "A307",

              qita: "",
            },
            {
              number: "LAB004",
              nanme: "天平",
              pingpai: "品牌W",
              baozhuang: "2楼",
              xiaobaozhuang: "B209",

              qita: "",
            },
            {
              number: "LAB005",
              nanme: "烘箱",
              pingpai: "品牌V",
              baozhuang: "4楼",
              xiaobaozhuang: "C401",

              qita: "",
            },
          ],
        },
        {
          category: "计算机和信息化设备",
          items: [
            {
              number: "LAB001",
              nanme: "实验室电脑",
              pingpai: "品牌A",
              baozhuang: "3楼",
              xiaobaozhuang: "A308",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "数据采集设备",
              pingpai: "品牌B",
              baozhuang: "3楼",
              xiaobaozhuang: "A310",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "服务器",
              pingpai: "品牌C",
              baozhuang: "1楼",
              xiaobaozhuang: "机房",

              qita: "",
            },
          ],
        },
        {
          category: "办公设备",
          items: [
            {
              number: "LAB001",
              nanme: "打印机",
              pingpai: "品牌D",
              baozhuang: "2楼",
              xiaobaozhuang: "B205",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "复印机",
              pingpai: "品牌E",
              baozhuang: "2楼",
              xiaobaozhuang: "B206",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "投影仪",
              pingpai: "品牌F",
              baozhuang: "3楼",
              xiaobaozhuang: "A309",

              qita: "",
            },
          ],
        },
        {
          category: "基础设施",
          items: [
            {
              number: "LAB001",
              nanme: "实验台",
              pingpai: "品牌G",
              baozhuang: "4楼",
              xiaobaozhuang: "C402",

              qita: "",
            },
            {
              number: "LAB002",
              nanme: "通风系统",
              pingpai: "品牌H",
              baozhuang: "5楼",
              xiaobaozhuang: "D501",

              qita: "",
            },
            {
              number: "LAB003",
              nanme: "实验室椅子",
              pingpai: "品牌I",
              baozhuang: "3楼",
              xiaobaozhuang: "A306",

              qita: "",
            },
          ],
        },
      ],
      input: "",
      activeTab: "today",
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,

      tableDataItem: [],
      tableTitle: [
        { key: "" },
        { key: "资产名称" },
        { key: "资产品牌" },
        { key: "楼层" },
        { key: "房间号" },

        { key: "其他说明" },
      ],
      ids: null,
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          name: "检测到烟雾，可能有着火灾的发生...",
          value: "",
          time: "09:13:59  2023-06-07",
        },
        {
          type: 2,
          name: "检测到烟雾，可能预示着火灾的发生..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
        {
          type: 3,
          name: "实验室内检测到漏水，可能来自冷凝水..",
          value: "",
          time: "10:13:18  2023-06-12",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",

      componentTag: "component0",
      sbtitle: "",
      isFirstTime: true,
      cgqname: "传感器",
      detalis: [], // 用于标记是否是第一次调用
      chartData: [],
      dataUnit: "",
      chartOption: {
        title: {
          text: "",
          textStyle: {
            color: "#fff",
          },
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: [],
          axisLabel: {
            color: "#fff",
          },
        },
        yAxis: {
          type: "value",
          name: "",
          axisLabel: {
            color: "#fff",
            formatter: "{value}",
          },
        },
        series: [
          {
            type: "line",
            data: [],
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 2,
              color: "#37a2b6",
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgba(55, 162, 182, 0.5)",
                },
                {
                  offset: 1,
                  color: "rgba(55, 162, 182, 0.1)",
                },
              ]),
            },
          },
        ],
      },
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    async fetchProjectSet(type, projectId, parkId, buildId, floorId) {
      console.log(this.getname, type, projectId, parkId, buildId, floorId);
      //从接口拿数据
      try {
        const response = await axios.get(
          "https://api-dh3d-public.3dzhanting.cn:8081/projectSet/all",
          {
            params: {
              type: type,
              projectId: projectId,
              deviceId: "", // 如果不需要 deviceId，可以将其删除或保留为空字符串
              parkId: parkId,
              buildId: buildId,
              loorId: "",
              name: "氧气传感器,氢气传感器,二氧化碳传感器,氨气传感器,甲烷传感器,",
              roomId: "",
            },
          }
        );
        // this.sblist = response.data.data
        console.log(response.data.data, "处理好的数据");
        // 将楼层排序
        const floorOrder = ["5F", "4F", "3F", "2F", "1F", "B1F"]; // 定义排序顺序
        // 按楼层顺序排序
        const sortedData = response.data.data.sort(
          (a, b) =>
            floorOrder.indexOf(a.floorId) - floorOrder.indexOf(b.floorId)
        );
        const roomMap = {
          B106: "电镜辅助间 (B106)",
          B154: "透射电镜室1 (B154)",
          B153: "透射电镜室2 (B153)",
          B144: "电镜辅助间 (B144)",
          B109: "电镜辅助间 (B109)",
          B156: "透射电镜室3 (B156)",
          B155: "透射电镜室4 (B155)",
          B141: "电镜辅助间 (B141)",
          B110: "电镜辅助间 (B110)",
          B138: "透射电镜室5 (B138)",
          B137: "透射电镜室6 (B137)",
          B140: "电镜辅助间 (B140)",
          B113: "电镜制样间1 (B113)",
          B132: "电镜制样间 (B132)",
          B130: "电镜制样间 (B130)",
          B117: "扫描电镜5 (B117)",
          B118: "电镜辅助间 (B118)",
          B119: "扫面电镜6 (B119)",
          B116: "扫面电镜1 (B116)",
          B120: "电镜辅助间 (B120)",
          B122: "扫面电镜2 (B122)",
          B122: "扫面电镜3 (B122)",
          B123: "电镜辅助间 (B123)",
          B124: "扫面电镜4 (B124)",
          B145: "电镜辅助间 (B145)",
          B146: "扫面电镜7 (B146)",
          B147: "电镜辅助间 (B147)",
          B148: "透射电镜室8 (B148)",
          B150: "透射电镜室7 (B150)",
          B151: "电镜辅助间 (B151)",
          119: "核磁主机房 (119)",
          118: "核磁主机房 (118)",
          115: "样品处理间 (115)",
          116: "仪器室 (116)",
          120: "辅助机房 (120)",
          124: "核磁主机房 (114)",
          127: "核磁中心 (127)",
          125: "办公室 (125)",
          123: "空压机房 (123)",
          110: "能谱仪室 (110)",
          103: "中控室 (103)",
          104: "D射线衍射仪 (104)",
          105: "样品处理间 (105)",
          109: "D射线衍射仪2 (109)",
          204: "通用实验室 (204)",
          205: "通用实验室 (205)",
          218: "样品准备间 (218)",
          219: "汇流排间 (219)",
          220: "液相色谱 (220)",
          222: "样品处理室 (222)",
          223: "气相色谱 (223)",
          225: "光谱仪室 (225)",
          226: "光谱仪室 (226)",
          228: "样品处理间 (228)",
          231: "光谱仪室2 (231)",
          配电间外: "配电间外",
          上空: "上空",
          汇流排间: "汇流排间",
        };
        // 转换数据为所需格式
        const transformedData = sortedData.reduce((result, item) => {
          // 确保 floorId 存在且有效
          if (!item.floorId) return result;

          // 查找气瓶间菜单项
          let qipingMenu = result.find((menu) => menu.id === "menu5");
          if (!qipingMenu) {
            qipingMenu = {
              id: "menu5",
              title: "气瓶间",
              submenu: [],
            };
            result.push(qipingMenu);
          }

          // 查找或创建楼层子菜单
          let floorSubmenu = qipingMenu.submenu.find(
            (sub) => sub.id === `submenu5-${item.floorId}`
          );
          if (!floorSubmenu) {
            floorSubmenu = {
              id: `submenu5-${item.floorId}`,
              title: `${item.floorId}`,
              submenu: [],
            };
            qipingMenu.submenu.push(floorSubmenu);
          }

          // 添加传感器信息
          const sensorInfo = {
            title: `${item.roomId}-${item.name}`,
            content: `${item.roomId}-${item.name}`,
            cgqlist: [
              {
                name: "温度",
                value: item.temperature ? `${item.temperature}℃` : "20℃",
              },
              {
                name: "湿度",
                value: item.humidity ? `${item.humidity}%RH` : "15%RH",
              },
            ],
            deviceid: item.deviceId,
            id: item.id,
          };

          // 检查是否已存在相同的传感器
          const existingSensor = floorSubmenu.submenu.find(
            (sensor) => sensor.deviceid === item.deviceId
          );
          if (!existingSensor) {
            floorSubmenu.submenu.push(sensorInfo);
          }

          return result;
        }, []);

        console.log(transformedData, "处理好的数据");
        this.menus = transformedData;
        return response.data;
      } catch (error) {
        console.error("Error fetching project set:", error);
        throw error; // 重新抛出错误以便调用者处理
      }

      //从config拿数据
      // 将 name 字符串按逗号分隔为数组
      // const nameArray = this.getname.split(',').map(item => item.trim());
      // console.log(this.alldeviceList, nameArray, parkId, buildId, floorId, 'alldeviceList');
      // // 过滤设备列表，返回符合条件的设备
      // this.sblist = this.alldeviceList.filter(device => {
      //   const buildMatch = buildId == '' || device.buildId == buildId;
      //   const floorMatch = floorId == '' || device.floorId == floorId;
      //   const nameMatch = nameArray.includes(device.name);
      //   return device.parkId == parkId && buildMatch && floorMatch && nameMatch;
      // });

      // console.log('Response data:', this.sblist);
      // this.sendToUE41('shebei', this.sblist);
    },
    changetit(index) {
      this.titactive = index;
      this.isshowwhat = !index;
      if (index == 1) {
        this.showdh = false;
        this.showdh1 = true;
        this.noAnimation = true;
      } else {
        this.showdh = true;
        this.showdh1 = false;
        this.noAnimation = false;
      }
    },
    hidedetailsss() {
      this.isshowsss = false;
    },
    closeshow() {
      this.isshowsss = false;
    },
    async setContent(content, index) {
      console.log(content, index, "设置内容");
      this.isshowsss = true;
      this.selectedItem = content.content;
      this.cgqname = content.title;

      // 获取设备数据并更新图表
      try {
        const res = await getDeviceData(content.deviceid);
        const res1 = await getDevicedetails(content.deviceid);
        console.log(res, "res22");
        console.log(res1.data.deviceDataBase, "res11");
        if (res.code === 200 && res.data) {
          // 更新图表数据
          const chartData = res.data.data || [];
          const times = chartData.map((item) => item.recordedAt.slice(11, 16));
          const values = chartData.map((item) => item.indication);
          console.log(times, values, "times, values");

          // 更新图表配置
          this.chartOption.xAxis.data = times;
          this.chartOption.series[0].data = values;
          // this.chartOption.yAxis.name = res.data.dataUnit;
          this.chartOption.title.text = res.data.dataUnit;

          // 如果图表实例存在，更新图表
          if (this.$refs.echarts2) {
            this.$refs.echarts2.setOption(this.chartOption);
          }
        }

        // 设置设备详情信息
        if (content.title.includes("温湿度")) {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: item.dmName,
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          this.yccontent =
            "传感器可以测量周围环境的温度，并将其转换为数字信号或模拟信号输出;在温度超过设定范围后触发报警或关闭设备，以保护应用的安全性。";
        } else if (content.title.includes("氧气")) {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: item.dmName,
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          this.yccontent =
            "氧气传感器可以测量氧气在空气中的浓度,并将数据传输到控制系统,以监测氧气供应是否充足,从而保证系统的正常运行。";
        } else if (content.title.includes("氢")) {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: item.dmName,
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          this.yccontent =
            "氢气传感器可以实时监测环境中氢气的浓度,当检测到氢气浓度超过预设阈值时及时报警,保障实验室安全。";
        } else if (content.title.includes("碳")) {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: "CO2浓度",
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          console.log(this.detalis, "二氧化碳传感器");
          this.yccontent =
            "二氧化碳传感器用于监测空气中二氧化碳的浓度,帮助评估通风效果和空气质量,确保实验环境安全。";
        } else if (content.title.includes("氨")) {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: item.dmName,
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          this.yccontent =
            "氨气传感器用于检测空气中氨气的浓度,当浓度超过安全阈值时发出警报,保护人员安全。";
        } else if (content.title.includes("甲烷")) {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: item.dmName,
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          this.yccontent =
            "甲烷传感器用于监测环境中甲烷气体的浓度,防止甲烷积累造成的安全隐患,保障实验室安全。";
        } else {
          this.detalis = [
            { name: "设备名称", value: content.title },
            ...res1.data.deviceDataBase.map((item) => ({
              name: item.dmName,
              value: `${item.dVal}${item.dDataUnit}`,
            })),
          ];
          this.yccontent =
            "压差传感器是一种用于测量两个物理参量差异的传感器,它可以测量液体或气体流动中两个位置之间的压差。";
        }
      } catch (error) {
        console.error("获取设备数据失败:", error);
      }
      this.selectedIndex = index;
      this.isFirstTime = false;

      if (content.id) {
        this.$emit("seedid", content.id);
      }
    },

    // 切换二级菜单显示/隐藏
    toggleSubMenu(menuId, title, index) {
      this.activeSubmenu = this.activeSubmenu === menuId ? null : menuId;
      this.sbtitle = index;
      // this.$emit("seedbuild", "实验楼"); // 触发父组件的事件并传递数据
      // this.$emit(
      //   "seedfloor",
      //   title,
      //   "",
      //   "氧浓度传感器,温湿度传感器,压力传感器",
      //   true
      // ); // 触发父组件的事件并传递数据
      this.$emit("changecheck");
    },
    // 切换三级菜单显示/隐藏
    toggleSubSubMenu(submenuId, title, index) {
      this.activeSubSubmenu =
        this.activeSubSubmenu === submenuId ? null : submenuId;
      this.selectedIndex = null;
      this.sbtitle1 = index;
      this.$emit("seedbuild", "实验楼"); // 触发父组件的事件并传递数据
      this.$emit(
        "seedfloor",
        title,
        "",
        "氧气传感器,氢气传感器,二氧化碳传感器,氨气传感器,甲烷传感器,",
        true
      ); // 触发父组件的事件并传递数据
    },

    showdetails(item) {
      console.log(item.items);

      this.isshow = true;
      this.tableDataItem = item.items;
    },
    hidedetails() {
      this.isshow = false;
    },
    oc(value) {
      console.log(value, "收到的值");
      this.showdh = value;
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.fetchProjectSet(1, "YIHuaomzuSKUtXFCRYbdqA==", 0, "实验楼", "");
    this.showdh1 = true;
    var that = this;
    // setTimeout(() => {
    //   this.showdh1 = false;
    //   this.noAnimation = false;
    // }, 1000); // 动画持续时间为1秒
    console.log(1222);
    window.addEventListener("message", function (event) {
      //event.data获取传过来的数据
      let sbname;
      // let name = event.data.name;
      console.log(event, 517);
      if (event.data.type == "shebei") {
        console.log(event.data.data.id, "shebeiid");
        console.log(that.menus[that.sbtitle].submenu, "shebei123");
        let result = [];
        let menu = that.menus[that.sbtitle];
        let deviceIdResult = null; // 用于存储设备ID
        let submenuIndexResult = null; // 用于存储submenu索引
        // console.log(menu, 'menu');
        // 遍历所有submenu
        for (let i = 0; i < menu.submenu.length; i++) {
          console.log(menu.submenu[i], 121);
          const submenu = menu.submenu[i];
          deviceIdResult = submenu.id;
          // 遍历submenu中的所有设备
          for (let j = 0; j < submenu.submenu.length; j++) {
            const device = submenu.submenu[j];
            // console.log(device.id, event.data.data.id, 2024);
            // 如果设备ID匹配
            if (device.id == event.data.data.id) {
              submenuIndexResult = j; // 获取submenu的索引
              let idid = device.deviceid;
              console.log(device);
              sbname = device.title;
              // let jk = that.jkdata.find(item => item.deviceid == idid)
              // that.detalis =
              //   [
              //     { name: "设备名称", value: jk.fid + '-摄像头-' + jk.number },
              //     // { name: "监视区域：", value: "3F通风实验室" },
              //     { name: "品牌型号：", value: "天地伟业" },
              //     { name: "IP地址：", value: jk.ip },
              //     { name: "设备类型：", value: "高清枪型摄像机" },
              //   ];
              // 获取设备ID

              break; // 找到后退出
            }
          }

          if (submenuIndexResult !== null) break;
          // 如果找到了设备ID，则退出外层循环
        }
        that.activeSubSubmenu = deviceIdResult;
        that.selectedIndex = submenuIndexResult;
        console.log("sy", deviceIdResult, submenuIndexResult);
        // let title=this.menus[]
        if (sbname.includes("温湿度")) {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "温度", value: "21.5℃" },
            { name: "湿度", value: "53.8%" },
          ];
          that.yccontent =
            "传感器可以测量周围环境的温度，并将其转换为数字信号或模拟信号输出;在温度超过设定范围后触发报警或关闭设备，以保护应用的安全性。";
        } else if (sbname.includes("氧")) {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "氧浓度", value: "1Pa" },
          ];
          that.yccontent =
            "氧气传感器可以测量氧气在空气中的浓度,并将数据传输到控制系统,以监测氧气供应是否充足,从而保证系统的正常运行。";
        } else if (sbname.includes("氢")) {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "氢气浓度", value: "1Pa" },
          ];
          that.yccontent =
            "氢气传感器可以实时监测环境中氢气的浓度,当检测到氢气浓度超过预设阈值时及时报警,保障实验室安全。";
        } else if (sbname.includes("二氧化碳")) {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "二氧化碳浓度", value: "1Pa" },
          ];
          that.yccontent =
            "二氧化碳传感器用于监测空气中二氧化碳的浓度,帮助评估通风效果和空气质量,确保实验环境安全。";
        } else if (sbname.includes("氨")) {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "氨气浓度", value: "1Pa" },
          ];
          that.yccontent =
            "氨气传感器用于检测空气中氨气的浓度,当浓度超过安全阈值时发出警报,保护人员安全。";
        } else if (sbname.includes("甲烷")) {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "甲烷浓度", value: "1Pa" },
          ];
          that.yccontent =
            "甲烷传感器用于监测环境中甲烷气体的浓度,防止甲烷积累造成的安全隐患,保障实验室安全。";
        } else {
          that.detalis = [
            { name: "设备名称", value: sbname },
            // { name: "监视区域：", value: "3F通风实验室" },
            { name: "压差", value: "20%" },
          ];
          that.yccontent =
            "压差传感器是一种用于测量两个物理参量差异的传感器,它可以测量液体或气体流动中两个位置之间的压差。";
        }
        // that.activeSubSubmenu=that.menus[that.sbtitle].submenu
        //           .flatMap(submenuGroup => submenuGroup.submenu)
        //           .find(submenu => submenu.id == event.data.data.id).
      }
    });
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.botbtn {
  position: fixed;
  top: 978px;
  right: 745px;
  // right: 552px;
  width: 200px;
  height: 43px;
  background: #022d56;
  border-radius: 8px;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;

  .btt {
    color: #fff;
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }

  .btt1 {
    color: rgb(8, 207, 241);
    font-size: 17px;
    line-height: 44px;
    width: 88px;
    text-align: center;
    height: 43px;
    cursor: pointer;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .sbdetails {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .box {
    margin-top: 6px;
    padding-top: 5px;
    margin-bottom: 0.225rem;
    // height: 800px;
    width: 360px;
    // height: 800px;

    overflow-y: scroll;

    /* 设置垂直滚动条 */
    /* 设置滚动条的样式 */
    &::-webkit-scrollbar {
      width: 0.1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    &::-webkit-scrollbar-thumb {
      background-color: #334f6e;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
      /* 设置鼠标悬停时滚动条滑块的背景色 */
    }

    .el-input {
      width: 335px;
      height: 39px;
      color: #fff !important;

      ::v-deep .el-input__wrapper {
        background: url("../assets/image/inputss.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        box-shadow: none !important;
      }
    }

    .suosuo {
      position: absolute;
      top: 66px;
      left: 285px;
    }

    .xiaobox {
      margin-top: 20px;
      display: flex;
      align-items: center;

      .siqiu {
        width: 16px;
        height: 16px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
      }
    }

    .xiaoboxs {
      cursor: pointer;
      margin-top: 14px;
      display: flex;
      margin-left: 5px;
      align-items: center;

      .nihaowo {
        width: 78px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        display: flex;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }

      .siqiu {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        margin-right: 7px;
      }

      .shuru {
        display: flex;
        align-items: center;
        padding-left: 10px;
        background: url("../assets/image/shebei12.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 261px;
        height: 32px;
        font-family: Source Han Sans SC;
        font-weight: bold;
        font-size: 17px;
        color: #ffffff;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }
    }
  }

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 100px;
    left: 22px;
    width: 330px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 83px;
    width: 330px;
    top: 100px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .jk {
      margin-top: 12px;
      width: 90%;
      height: 200px;
    }

    .box {
      margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 94%;
      // height: 428px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;

        .h2biaoti {
          margin-bottom: 15px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #00ffff;
        }
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }

    .boxxx {
      margin-top: 6px;
      margin-bottom: 18px;
      background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 333px;
      height: 420px;
      overflow-y: scroll;
      /* 设置垂直滚动条 */
      // overflow: hidden;
      /* 设置滚动条的样式 */

      .zengti {
        margin: 10px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 315px;
        height: 38px;
        gap: 5px;

        .left {
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          width: 84px;
          height: 27px;

          .yuan {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #08f7f7;
          }

          .wenziss {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .p1 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #ffffff;
            }

            .p2 {
              font-family: Source Han Sans SC;
              font-weight: 500;
              font-size: 10px;
              color: #55cff9;
            }
          }
        }

        .right {
          background: url("../assets/image/rightbeij.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;

          width: 217px;
          height: 38px;
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 11px;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .boxxx::-webkit-scrollbar {
      width: 1px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .boxxx::-webkit-scrollbar-track {
      background-color: #f1f1f1;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .boxxx::-webkit-scrollbar-thumb {
      background-color: #013363;
      /* 设置滚动条滑块的背景色 */
    }
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  margin-top: 20px;
  justify-content: space-around;
  margin-top: 14px;

  .ql-Box {
    width: 75px;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 16px;
      color: #7ad0ff;
      margin-top: -10px;
    }

    .ql-box {
      display: flex;
      padding-left: 8px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 34px;

      .left_ql {
        width: 49px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 7px;
          height: 7px;
          border-radius: 50%;
        }

        .pp {
          color: #fff;
          font-size: 12px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  background: url("../assets/image/warnred.png");
}

.warn2 {
  background: url("../assets/image/warnyellow.png");
}

.warn3 {
  background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;

    margin-left: 50px;

    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
        font-size: 13px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-size: 16px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }
    }

    .info2 {
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}

.xxxx {
  position: absolute;
  top: 1%;
  right: 1%;
  width: 25px;
  height: 25px;
  z-index: 99999;
}

/* 菜单容器 */
.menu-container {
  display: flex;
  gap: 20px;
}

.menu {
  height: 760px;
  width: 340px;
  // background-color: #fff;
  // border: 1px solid #ddd;
  border-radius: 4px;
  overflow-y: auto;
  margin-left: 13px;
}

/* 设置滚动条的样式 */
.menu::-webkit-scrollbar {
  width: 3px;
  /* 设置滚动条的宽度 */
}

/* 设置滚动条轨道的样式 */
.menu::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  /* 设置滚动条轨道的背景色 */
}

/* 设置滚动条滑块的样式 */
.menu::-webkit-scrollbar-thumb {
  background-color: #163561;
  /* 设置滚动条滑块的背景色 */
}

/* 鼠标悬停在滚动条上时的样式 */
.menu::-webkit-scrollbar-thumb:hover {
  background-color: #555;
  /* 设置鼠标悬停时滚动条滑块的背景色 */
}

/* 菜单项样式 */
.menu-group {
  margin-top: 14px;
}

.menu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  width: 100%;
  height: 36px;
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 20px;
  color: #ffffff;
  display: flex;
  align-items: center;
  padding-left: 10px;
  // justify-content: center;
}

.menu-item:hover {
  background-color: #f0f0f0 !important;
  color: #00ffc0;
}

.submenu {
  display: block;
  /* 确保子菜单是块级元素 */
  // background-color: #f9f9f9;
  padding-left: 8px;
}

.submenu-items:hover {
  color: #00ffc0;
}

.submenu-item:hover {
  color: #00ffc0;
}

.submenu-items {
  cursor: pointer;
  // background-color:#013363;
  width: 100%;

  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 18px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  flex-direction: column;
  /* 垂直方向排列 */
  align-items: flex-start;
  padding-left: 10px;
  margin-top: 10px;
}

.submenu-item {
  cursor: pointer;
  background: url("../assets/image/rightbeij.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 300px !important;
  /* 移除固定高度 */
  /* height: 30px; */
  font-family: Source Han Sans SC;
  font-weight: 500;
  font-size: 15px;
  color: #ffffff;
  /* 修改 display 属性 */
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-left: 10px;

  margin-top: 10px;
}

.qiuqiu {
  display: flex;
  align-items: center;

  .siqiu {
    width: 16px;
    height: 16px;
    margin-left: 10px;
    margin-right: 7px;
  }
}

.listtype {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 80px;
  height: 26px;
  text-align: center;
  line-height: 26px;

  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 36px;
  text-align: center;
  line-height: 36px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  font-size: 15px;
}

.listtypes {
  background: url("../assets/image/xuanzhotiemmoon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100px;
  height: 32px;
  text-align: center;
  line-height: 32px;
  cursor: pointer;
  font-size: 13px;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  color: #fff;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 190px;
  /* 你可以根据需要调整宽度 */
  font-size: 15px;
}

.hr {
  margin-top: 24px;
  margin-bottom: 25px;
  width: 100%;
  background-color: rgba(36, 101, 138, 1);
  color: rgba(36, 101, 138, 1);
}

.local {
  text-align: left !important;

  font-family: Source Han Sans SC;
  font-weight: bold;
  font-size: 17px;
  color: #ffffff;

  // display: flex;
  // justify-content: space-between;
  .lianxi {
    margin-left: 118px;
  }
}
</style>
