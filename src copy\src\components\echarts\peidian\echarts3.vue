<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: ["echartData"],
  data() {
    return {};
  },
  watch: {
    echartData(newVal) {
      this.init();
    },
  },
  mounted() {
    this.init();
  },

  methods: {
    init() {
      let data = this.echartData;

      const myChart = echarts.init(this.$refs.echart);

      const option = {
        color: ["#5FC7F8"],
        legend: {
          data: ["今日"],
          right: 0,
          textStyle: {
            fontSize: 10, //字体大小
            color: "#77CCFF", //字体颜色
          },
        },
        title: {
          text: "KW",
          textStyle: {
            align: "left",
            color: "#00FFFF",
            fontSize: 12,
          },
          top: "5%",
          left: "40",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        grid: {
          left: "10%",
          right: "10%",
          bottom: "0%",
          top: 40,
          containLabel: true,
        },
        xAxis: {
          boundaryGap: false,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#B0C0D1",
              opacity: 0.3, //刻度线的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(0,176,255,0.1)"],
              width: 1,
              type: "solid",
            },
          },
          axisTick: {
            //x轴刻度线
            show: false,
          },

          axisLabel: {
            textStyle: {
              color: "#eee",
              fontSize: 11,
            },
            align: "right",
          },
          data: data.xAxis,
        },
        yAxis: {
          type: "value",

          show: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#B0C0D1",
              opacity: 0.3,
            },
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: ["rgba(0,176,255,0.1)"],
              width: 1,
              type: "solid",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#fff",
              fontSize: 11,
            },
          },
        },
        series: [
          {
            type: "line",
            smooth: false,
            showSymbol: true,
            symbol: "rect", // 数据点的形状为矩形
            symbolSize: 5,
            zlevel: 5,
            itemStyle: {
              color: "#ED8532", // 数据点填充颜色
              borderColor: "#ED8532", // 数据点边框颜色
            },
            lineStyle: {
              normal: {
                width: 2,
                color: "#ED8532",
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#335056",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(237,133,50,0)",
                    },
                  ],
                  false
                ),
              },
            },
            data: data.series2,
          },

          {
            type: "line",
            smooth: false,
            showSymbol: true,
            symbol: "rect", // 数据点的形状为矩形
            symbolSize: 5,
            zlevel: 5,
            itemStyle: {
              color: "#2E5F5D", // 数据点填充颜色
              borderColor: "#2E5F5D", // 数据点边框颜色
            },
            lineStyle: {
              normal: {
                width: 2,
                color: "#2E5F5D",
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#2E5F5D",
                    },
                    {
                      offset: 0.8,
                      color: "rgba( 49, 99, 96,0)",
                    },
                  ],
                  false
                ),
              },
            },
            data: data.series1,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  // margin-top: 40px;
  width: 100%;
  height: 192px;
}

// @media (max-height: 13.5rem) {
//   .echart {
//     width: 377px;
//     height: 160px !important;
//   }
// }
</style>