import { createStore } from 'vuex';
import createPersistedState from 'vuex-persistedstate'; // 导入插件
import equipment from './modules/equipment';

const store = createStore({
  modules: {
    equipment,
  },
  plugins: [
    createPersistedState({
      key: 'vuex', // 存储的 key
      paths: [
        'equipment.equipmentRank', // 仪器使用排行
        'equipment.yiqiztlist',    // 仪器实时状态
        'equipment.ryfblist',      // 人员分布统计
        'equipment.ktcslist',      // 课题测试统计
        'equipment.ktzlist',       // 课题组使用统计
        'equipment.equipmentTags',       // 课题组使用统计
      ], 
      storage: window.localStorage, // 使用 localStorage
    }),
  ],
});

// 在 Vuex store 初始化时立即请求数据
// 如果需要加载初始数据，请确保在 store 实例初始化时调用请求：
// store.dispatch('equipment/fetchEquipmentRank'); 

export default store;
