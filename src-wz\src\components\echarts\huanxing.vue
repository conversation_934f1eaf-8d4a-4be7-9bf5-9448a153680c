<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  props: {
    warningData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  watch: {
    warningData: {
      handler() {
        this.init();
      },
      deep: true,
    },
  },

  methods: {
    init() {
      const myChart = echarts.init(this.$refs.echart);
      console.log(this.warningData,'this.warningData');

      const data =
        this.warningData.length > 0
          ? this.warningData
          : [
              {
                label: "暂无数据",
                value: 100,
              },
            ];

      const colors = [
        "37, 171, 200",
        "214, 128, 120",
        "252, 182, 53",
        "47, 255, 242",
        "42, 191, 191",
        "60, 179, 113",
        "255, 165, 0",
        "138, 43, 226",
        "255, 99, 71",
      ];

      const option = {
        legend: {
          top: "5%",
          right: "-1%",
          data: data.map((it) => it.label),
          textStyle: {
            color: "#fff",
            fontSize: 16,
            fontFamily: "Microsoft YaHei",
          },
          itemWidth: 12,
          itemHeight: 12,
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)",
          textStyle: {
            fontSize: 10,
          },
        },
        series: [
          {
            right: "0%",
            bottom: "15%",
            name: "报警",
            type: "pie",
            radius: ["30%", "75%"],
            center: ["50%", "65%"],
            roseType: "radius",
            label: {
              show: true,
              normal: {
                position: "outside",
                fontSize: 18,
                formatter: "{d}%",
                color: "#fff",
              },
            },
            labelLine: {
              length: 1,
              length2: 7,
            },
            data: data.map((it, i) => {
              return {
                value: it.value,
                name: it.label,
                itemStyle: {
                  color: `rgba(${colors[i % colors.length]},0.7)`,
                  borderColor: `rgba(${colors[i % colors.length]},1)`,
                  borderWidth: 1,
                },
              };
            }),
          },
        ],
      };
      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 110%;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 110% !important;
  }
}
</style>