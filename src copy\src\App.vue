<template>
  <router-view @contextmenu="preventRightClick" />
</template>
<script>

import { provide } from "vue"
import * as echarts from 'echarts';
// import axios from "axios"
// import Header from '@/components/Header.vue';


export default {
  methods: {
    preventRightClick(event) {
      event.preventDefault();
    }
  },
  setup() {
    provide("echarts", echarts);
    // provide("axios", axios)
  },
  mounted() {
  },
  components: {
    // "v-head": Header

  }

}
</script>

<style lang="less">
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
// background: #2c3e50;
  color: #2c3e50;
  position: relative;

}

* {

  margin: 0;
  padding: 0;
  box-sizing: border-box;

}
</style>
