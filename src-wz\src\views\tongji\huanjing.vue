<template>
  <div class="contents" v-if="isshow">
    <div class="toubu">
      <div
        style="margin-left: 20px; display: flex; align-items: center"
        v-if="false"
      >
        <div style="display: flex; width: 100%; align-items: center">
          <span class="sp">当前位置：</span>
          <el-select
            class="el-select"
            v-model="selectvalue1"
            placeholder="selectvalue1"
            style="width: 64px; height: 35.1px"
            @change="handleChange"
          >
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            class="el-select"
            v-model="selectvalue2"
            placeholder="selectvalue2"
            style="width: 64px; height: 35.1px"
            @change="handleChange"
          >
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            class="el-select"
            v-model="selectvalue3"
            placeholder="selectvalue3"
            style="width: 78px; height: 35.1px"
            @change="handleChange"
          >
            <el-option
              v-for="item in options3"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <img
          v-if="isshow"
          class="img1sss"
          @click="anniu()"
          src="../../assets/image/table-x.png"
          alt=""
        />
      </div>

      <div class="all">
        <div class="all1">
          <Titles class="ltitle" tit="传感器实时状态">
            <div class="shbei">
              <div class="item">
                <img src="../../assets/image/centerAcimg1.png" alt="" />
                <div class="numlist">
                  <div class="it1">196</div>
                  <div class="it2">传感器总数</div>
                </div>
              </div>
              <div class="item">
                <img src="../../assets/image/centerAcimg2.png" alt="" />
                <div class="numlist">
                  <div class="it1 it3">192</div>
                  <div class="it2">正常总数</div>
                </div>
              </div>
              <div class="item">
                <img src="../../assets/image/centerAcimg2.png" alt="" />
                <div class="numlist">
                  <div class="it1 it3">4</div>
                  <div class="it2">故障总数</div>
                </div>
              </div>
            </div>
            <echarts1></echarts1>
            <!-- <div class="nenghao">累计总能耗:</div>
            <p class="nhp">92673.0 kwh</p>
            <div class="nh">
              <img class="nhimg" src="../../assets/image/nenghao1.png" alt="" />
              <div class="nhtit">
                <p class="p11">122</p>
                <p class="p2">本日累计能耗</p>
              </div>
              <div class="nhtit1">
                <img class="nhimg1" src="../../assets/image/nhxia.png" alt="" />
                <p class="pp1">5%</p>
              </div>
            </div>
            <div class="nh">
              <img class="nhimg" src="../../assets/image/nenghao1.png" alt="" />
              <div class="nhtit">
                <p class="p11">23456</p>
                <p class="p2">近7日累计能耗</p>
              </div>
              <div class="nhtit1">
                <img class="nhimg1" src="../../assets/image/nhxia.png" alt="" />
                <p class="pp1">10%</p>
              </div>
            </div>
            <div class="nh">
              <img class="nhimg" src="../../assets/image/nenghao2.png" alt="" />
              <div class="nhtit">
                <p class="p12">4567999</p>
                <p class="p2">近30日累计能耗</p>
              </div>
              <div class="nht">
                <div class="nhtit1">
                  <img
                    class="nhimg1"
                    src="../../assets/image/nhshang.png"
                    alt=""
                  />
                  <p class="pp2">10%</p>
                </div>
                <p class="pp">环比</p>
              </div>
            </div> -->
          </Titles>
          <Titles class="ltitle11" tit="传感器实时报警">
            <!-- <huanxing style="margin-top:30px" :chartData="chartData"></huanxing> -->
            <!-- <div class="shinei">
              <Electricity4></Electricity4>
            </div> -->
            <div class="unfixed-warnings">
              <!-- 未修复警告列表 -->
              <div
                v-for="(warning, index) in warningData.unfixed"
                :key="'unfixed-' + index"
                class="warning12"
              >
                <div class="info">
                  <div>
                    <div class="zongduan">
                      <div class="yuan" style="background-color: #b93851"></div>
                      <div class="cjhulizhong" style="color: #b93851">
                        未修复
                      </div>
                    </div>
                    <p class="info2">{{ warning.warningCategory }}</p>
                  </div>

                  <div class="info1">
                    <p class="time">{{ formatDate(warning.createdAt) }}</p>
                    <p class="location">{{ warning.errMsg }}</p>
                  </div>
                  <p class="info2">
                    {{ warning.deviceName }}
                  </p>
                </div>
              </div>
            </div>
          </Titles>
        </div>
        <div class="line1"></div>
        <div class="all2">
          <Titles class="ltitle1" tit="温度传感器历史状态分析">
            <div class="shinei">
              <echarts3 class="echart3" :echartData="echartData2"></echarts3>
            </div>
          </Titles>
          <Titles class="ltitle" tit="湿度传感器历史状态分析">
            <div class="shinei">
              <echarts3 class="echart3" :echartData="echartData3"></echarts3>
            </div>
          </Titles>
          <!-- <div>
            <Titles class="ltitle1" tit="设备购入时间">
              <div class="shinei">
                <Electricity3 :chartData="chartData3"></Electricity3>
              </div>
            </Titles>
          </div> -->
        </div>
        <div class="all3">
          <Titles class="ltitle1" tit="压差传感器历史状态分析">
            <div class="shinei">
              <echarts3 class="echart3" :echartData="echartData4"></echarts3>
            </div>
          </Titles>
          <Titles class="ltitle1" tit="氧气传感器历史状态分析">
            <div class="shinei">
              <echarts3 class="echart3" :echartData="echartData5"></echarts3>
            </div>
          </Titles>
          <!-- <Titles class="ltitle1" tit="资产占比分析">
            <div class="shinei">
              <huanxing :chartData="chartData"></huanxing>
            </div>
          </Titles> -->
          <!-- <div class="shuantitle">
            <div style="width: 50%">
              <div class="title">实时负载率</div>
              <div class="nenghao">实时负载率:</div>
              <p class="nhp">30%</p>
            </div>
            <div style="width: 50%">
              <div class="title">实时总功率</div>
              <div class="nenghao">实时总功率:</div>
              <p class="nhp">200Kw</p>
            </div>
          </div>
      -->
        </div>
      </div>
    </div>
  </div>
</template> 

<script>
import Electricity from "@/components/echarts/dianbiao/biao1.vue";
import biao1s from "@/components/echarts/dianbiao/biao1s.vue";
import biao1ss from "@/components/echarts/dianbiao/biao1ss.vue";
import Titles from "@/components/common/Titles.vue";
import echarts1 from "@/components/huanjing/echarts1.vue";
import Electricity2 from "@/components/echarts/dianbiao/Electricity2.vue";
import Electricity3 from "@/components/fuyong//zhexiantu.vue";
import Electricity4 from "@/components/fuyong/Electricity4.vue";
import Electricity5 from "@/components/huanjing/Electricity5.vue";
import Electricity6 from "@/components/fuyong/Electricity6.vue";
import Electricity7 from "@/components/fuyong/Electricity7.vue";
// import Electricity7 from "@/components/echarts/dianbiao/Electricity7.vue";
import Electricity8 from "@/components/fuyong/Electricity8.vue";
import huanxing from "@/components/fuyong/xiaobingtu.vue";
import zhuzhuangtu from "@/components/fuyong/zhuzhuangtu.vue";
import echarts3 from "@/components/echarts/kongya/echarts2.vue";
import {
  getDeviceData,
  getDevicedetails,
  getDeviceWarningList,
} from "@/api/device.js";

export default {
  components: {
    echarts1,
    echarts3,
    Titles,
    Electricity,
    Electricity2,
    Electricity3,
    Electricity4,
    Electricity5,
    Electricity6,
    Electricity7,
    Electricity8,
    huanxing,
    biao1s,
    biao1ss,
    zhuzhuangtu,
  },
  data() {
    return {
      warningData: {
        unfixed: [],
        fixed: [],
        unfixedtotal: 0,
        fixedtotal: 0,
      },
      echartData2: {
        unit: "单位:个",
        legend: ["正常数", "故障数"],
        xdata: [
          // "11/3",
          "11/4",
          "11/5",
          "11/6",
          "11/7",
          "11/8",
          "11/9",
          "11/10",
          "11/11",
          "11/12",
          "11/13",
          "11/14",
          "11/15",
        ],
        ydata: [
          {
            name: "正常数",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [63, 62, 64, 63, 62, 61, 63, 67, 67, 64, 62, 63],
          },
          {
            name: "故障数",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [4, 5, 3, 4, 5, 4, 4, 0, 0, 3, 5, 4],
          },
        ],
      },
      echartData3: {
        unit: "单位:个",
        legend: ["正常数", "故障数"],
        xdata: [
          // "11/3",
          "11/4",
          "11/5",
          "11/6",
          "11/7",
          "11/8",
          "11/9",
          "11/10",
          "11/11",
          "11/12",
          "11/13",
          "11/14",
          "11/15",
        ],
        ydata: [
          {
            name: "正常数",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [66, 63, 64, 63, 60, 67, 63, 67, 64, 64, 65, 67],
          },
          {
            name: "故障数",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [1, 4, 3, 4, 7, 0, 4, 0, 3, 3, 2, 0],
          },
        ],
      },
      echartData4: {
        unit: "单位:个",
        legend: ["正常数", "故障数"],
        xdata: [
          // "11/3",
          "11/4",
          "11/5",
          "11/6",
          "11/7",
          "11/8",
          "11/9",
          "11/10",
          "11/11",
          "11/12",
          "11/13",
          "11/14",
          "11/15",
        ],
        ydata: [
          {
            name: "正常数",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [47, 47, 46, 46, 46, 47, 47, 47, 46, 46, 47, 47],
          },
          {
            name: "故障数",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [1, 0, 1, 1, 1, 0, 0, 0, 1, 1, 0, 1],
          },
        ],
      },
      echartData5: {
        unit: "单位:个",
        legend: ["正常数", "故障数"],
        xdata: [
          // "11/3",
          "11/4",
          "11/5",
          "11/6",
          "11/7",
          "11/8",
          "11/9",
          "11/10",
          "11/11",
          "11/12",
          "11/13",
          "11/14",
          "11/15",
        ],
        ydata: [
          {
            name: "正常数",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [13, 14, 13, 13, 10, 13, 13, 13, 13, 13, 13, 13],
          },
          {
            name: "故障数",
            type: "line",
            symbol: "none",
            smooth: true,
            data: [1, 0, 1, 1, 4, 1, 1, 1, 1, 1, 1, 1],
          },
        ],
      },
      chartData: {
        value: [1, 1, 1, 1],
        legend: ["温度传感器", "湿度传感器", "压差传感器", "氧气传感器"],
      },
      chartData1: {
        title: ["已领用", "未领用"],
        xAxisdata: ["20日", "21日", "22日", "23日", "25日", "24日"],
        yAxisdata1: [720, 710, 730, 705, 715, 725],
        yAxisdata2: [480, 490, 470, 495, 485, 475],
      },
      chartData2: {
        title: [" ", "未领用"],
        xAxisdata: ["20日", "21日", "22日", "23日", "25日", "24日"],
        yAxisdata1: [600, 580, 620, 590, 610, 605],
        yAxisdata2: [300, 320, 280, 310, 290, 295],
      },
      chartData3: {
        title: ["仪器设备", "办公设备"],
        xAxisdata: [
          "10/16",
          "10/17",
          "10/18",
          "10/19",
          "10/20",
          "10/21",
          "10/22",
          "10/23",
          "10/24",
          "10/25",
          "10/26",
          "10/27",
          "10/28",
        ],
        yAxisdata1: [4, 17, 5, 9, 6, 5, 0, 0, 12, 0, 4, 0, 1],
        yAxisdata2: [14, 7, 2, 3, 16, 5, 0, 0, 2, 0, 13, 0, 0],
      },
      isshow: true,
      options: [
        {
          value: "总览",
          label: "总览",
        },
        {
          value: "能耗分析",
          label: "能耗分析",
        },
        {
          value: "能流分析",
          label: "能流分析",
        },
        {
          value: "设备状态",
          label: "设备状态",
        },
        {
          value: "一键抄表",
          label: "一键抄表",
        },
        {
          value: "费用管理",
          label: "费用管理",
        },
        {
          value: "碳排放管理",
          label: "碳排放管理",
        },
      ],
      selectvalue2: "B3",
      options2: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B3",
      options3: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue1: "B3",
      options1: [
        {
          value: "B3",
          label: "B3",
        },
      ],
      selectvalue3: "B1栋",
      options4: [
        {
          value: "B1栋",
          label: "B1栋",
        },
        {
          value: "B2栋",
          label: "B2栋",
        },
        {
          value: "B3栋",
          label: "B3栋",
        },
        {
          value: "B4栋",
          label: "B4栋",
        },
        {
          value: "W1栋",
          label: "W1栋",
        },
        {
          value: "W2栋",
          label: "W2栋",
        },
      ],
      selectvalue4: "B1栋",
      optionData: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
      optionData1: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
    };
  },
  mounted() {
    this.fetchAllWarningData();
    // 每5分钟刷新一次数据
    setInterval(this.fetchAllWarningData, 5 * 60 * 1000);
  },
  methods: {
    formatDate(timestamp) {
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")} ${String(
        date.getHours()
      ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    },
    async fetchAllWarningData() {
      await Promise.all([this.getWarningList("N"), this.getWarningList("Y")]);
    },
    async getWarningList(hasFixed) {
      try {
        // 在关键请求前检查token是否需要刷新
        if (this.$auth && this.$auth.checkAndRefreshToken) {
          await this.$auth.checkAndRefreshToken();
        }

        const response = await getDeviceWarningList({
          hasFixed: hasFixed,
        });

        if (response.code === 200) {
          if (hasFixed === "N") {
            this.warningData.unfixed = response.rows;
            this.warningData.unfixedtotal = response.total;
          } else {
            this.warningData.fixed = response.rows;
            this.warningData.fixedtotal = response.total;
          }
          console.log(this.warningData, "this.warningData");
        } else {
          console.error("获取警告数据失败:", response.msg);
          // 只有在明确的认证错误时才清除token并跳转
          if (response.code === 401) {
            localStorage.removeItem("token");
            this.$router.push("/");
          }
        }
      } catch (error) {
        console.error("请求警告数据出错:", error);
        // 请求错误时不要立即清除token，让拦截器处理
      }
    },
    anniu() {
      this.isshow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.unfixed-warnings {
  margin-top: 10px;
  margin-left: 20px;
  width: 430px;
  height: 280px;
  overflow-y: auto;
}
/* 设置滚动条的样式 */
.unfixed-warnings::-webkit-scrollbar {
  width: 5px; /* 设置滚动条的宽度 */
}

/* 设置滚动条轨道的样式 */
.unfixed-warnings::-webkit-scrollbar-track {
  background-color: #454f5d; /* 设置滚动条轨道的背景色 */
}

/* 设置滚动条滑块的样式 */
.unfixed-warnings::-webkit-scrollbar-thumb {
  background-color: #f1f1f1; /* 设置滚动条滑块的背景色 */
}

.warning12 {
  background-size: 100% 100%;
  height: 47px;
  margin-bottom: 8px;

  .info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    text-align: left;
    font-size: 13px;
    padding: 8px 12px;
    background: rgba(25, 37, 60, 0.1);
    border-radius: 4px;

    .zongduan {
      display: flex;
      align-items: center;
      min-width: 80px;

      .yuan {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
      }

      .cjhulizhong {
        font-family: Microsoft YaHei;
        font-weight: bold;
        font-size: 14px;
      }
    }

    .info1 {
      flex: 1;
      // margin: 0 12px;

      .time {
        font-family: Microsoft YaHei;
        font-size: 14px;
        color: #ffffff;
        margin-bottom: 4px;
      }

      .location {
        font-family: Microsoft YaHei;
        font-size: 14px;
        color: #ffffff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .info2 {
      cursor: pointer;
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #b93851;
      // white-space: nowrap;
      // margin-left: 10px;
    }
  }
}

.zhuzhuangtu {
  margin-top: 20px;
  margin-bottom: 10px;
}

.shbei {
  margin-top: 10px;

  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 411px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-around;

  .item {
    display: flex;

    img {
      width: 58px;
      height: 56px;
    }

    .numlist {
      margin-left: 2px;

      .it1 {
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 25px;
        color: #ffffff;
      }

      .it2 {
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 16px;
        color: #00ffff;
      }

      .it3 {
        color: #00ffcc;
      }
    }
  }
}

.all {
  display: flex;
  flex-direction: row;
  margin-top: 5px;

  .zong {
    display: flex;
    flex-direction: row;
    margin-top: 10px;

    .echart1,
    .echart2 {
      flex: 1;

      .center {
        margin-top: -24px;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: 400;
        font-size: 17px;
        color: #00ffb6;
        text-align: center;
        margin-bottom: 10px;
      }

      .btn {
        width: 133px;
        height: 31px;
        border: 1px solid #2d6cb0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Source Han Sans SC", sans-serif;
        font-weight: bold;
        font-size: 15px;
        color: #ffffff;
        border-radius: 30px;
        margin-left: 7%;
      }
    }
  }

  .ltitle {
    margin-top: 10px;
  }

  .ltitle1 {
    margin-top: 10px;
  }

  .ltitle11 {
    margin-top: 30px;
  }

  .line1 {
    width: 2px;
    height: 823px;
    opacity: 0.64;
    background-color: #204964;
  }

  .all1 {
    flex: 562;

    .nenghao {
      width: 227px;
      height: 173px;
      background: url("../../assets/image/nenghao.png");
      background-size: 100% 100%;
      margin-left: 120px;
      margin-top: 21px;
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      line-height: 213px;
    }

    .nhp {
      text-align: center;
      font-family: Alibaba PuHuiTi;
      font-weight: 500;
      font-size: 30px;
      color: #2cc1ff;
      margin-top: 8px;
    }

    .nh {
      margin-left: 24px;
      margin-top: 5px;
      width: 423px;
      height: 93px;
      border: 1px solid #364d5a;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding-left: 42px;

      .nhimg {
        width: 96.6px;
        height: 70px;
      }

      .nhtit {
        width: 148px;
        margin-left: 10px;
        margin-top: 10px;

        .p11 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #7acfff;
        }

        .p12 {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 30px;
          color: #ffa170;
        }

        .p2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffffff;
        }
      }

      .nhtit1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-left: 35px;

        .nhimg1 {
          width: 16px;
          height: 20px;
        }

        .pp1 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #0df29b;
        }

        .pp2 {
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;
          color: #ffa170;
        }
      }

      .nht {
        margin-top: 10px;
        display: flex;
        flex-direction: column;

        .pp {
          margin-left: 35px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 17px;

          color: #cccccc;
        }
      }
    }
  }

  .all2 {
    margin-left: 38px;
    flex: 667;
    display: flex;
    flex-direction: column;

    .shinei {
      .itemshei {
        display: flex;
        justify-content: space-around;

        .nenghaos {
          width: 227px;
          height: 173px;
          background: url("../../assets/image/nenghao.png");
          background-size: 100% 100%;
          text-align: center;
          margin-left: 10px;
          margin-top: 23px;
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
          line-height: 144px;
        }

        .nhps {
          text-align: center;
          font-family: Alibaba PuHuiTi;
          font-weight: 500;
          font-size: 21px;
          color: #2cc1ff;
          margin-top: 8px;
        }
      }
    }
  }

  .all3 {
    flex: 668;
    margin-left: 45px;
  }
}

.shinei {
  width: 100%;
  height: 100%;
}

.shuantitle {
  width: 100%;
  display: flex;
  margin-top: 10px;

  .title {
    width: 95%;
    background: url("../../assets/image/title.png");
    background-size: 100% 100%;

    height: 25px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 25px;
    color: #ffffff;
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);
    font-style: italic;
    text-align: left;
    line-height: 4px;
    padding-left: 33px;
  }
}

.nenghao {
  width: 167px;
  height: 113px;
  background: url("../../assets/image/nenghao.png");
  background-size: 100% 100%;
  text-align: center;
  margin-left: 83px;
  margin-top: 63px;
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 144px;
}

.nhp {
  text-align: center;
  font-family: Alibaba PuHuiTi;
  font-weight: 500;
  font-size: 21px;
  color: #2cc1ff;
  margin-top: 8px;
}

.contents {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: url("../../assets/image/zichanbeijin.png");
  background-size: 100% 100%;
  width: 1863px;
  height: 868px;
  z-index: 99999;
  padding-left: 34px;
  padding-right: 22px;
  padding-top: 21px;
}

.toubu {
  width: 100%;

  position: relative;
}

.el-select {
  margin-top: -1px;
  margin-left: 10px;
  background: #00203d;
  border-radius: 3px;
  border: 1px solid #3e89db;

  /deep/.el-select__wrapper {
    background: #00203d !important;
    box-shadow: none;
  }

  /deep/.el-select__wrapper .is-hovering:not {
    box-shadow: none;
  }

  /deep/.el-select__wrapper:hover {
    box-shadow: none;
  }

  /deep/.el-select__placeholder.is-transparent {
    color: #2cc1ff;
  }

  /deep/.el-select__placeholder {
    color: #2cc1ff;
  }

  /deep/.el-select-dropdown__item.is-hovering {
    background-color: #2cc1ff !important;
  }
}

.sp {
  margin-top: -5px;
  margin-left: 12px;
  font-family: Alibaba PuHuiTi;
  font-weight: bold;
  font-size: 21px;
  color: #2cc1ff;
}

.img1sss {
  cursor: pointer;
  width: 15px;
  height: 15px;
}
</style>