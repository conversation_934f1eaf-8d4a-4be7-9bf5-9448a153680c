import axios from "axios";

// Vuex 状态管理
const state = () => ({
  equipmentRank: { yAxisdata: [], xAxisdata1: [] }, // 仪器使用排行
  yiqiztlist: [], // 仪器实时状态
  ryfblist: [], // 人员分布
  ktcslist: [], // 课题测试统计
  ktzlist: [], // 课题组使用统计
});

// Mutation 定义
const mutations = {
  SET_EQUIPMENT_RANK(state, data) {
    console.log("更新仪器使用排行:", data);
    state.equipmentRank = data;
  },
  SET_YIQI_STATUS(state, data) {
    console.log("更新仪器实时状态:", data);
    state.yiqiztlist = data;
  },
  SET_USER_DISTRIBUTION(state, data) {
    console.log("更新人员分布:", data);
    state.ryfblist = data;
  },
  SET_TEST_STATISTICS(state, data) {
    console.log("更新课题测试统计:", data);
    state.ktcslist = data;
  },
  SET_TOP_USERS(state, data) {
    console.log("更新用户排行:", data);
    state.ktzlist = data;
  },
};

// API 配置
const baseURL = process.env.VUE_APP_BASE_API || "/lims/api";
const api = axios.create({ baseURL });
const headers = {
  clientid: "5a298e93-158d-4e22-83cf-6ceb62e9b4f1",
  clientsecret: "2c8ec39e-9887-482a-b28b-e64c496b601c",
};

// 通用请求函数
const apiRequest = async (method, params = {}, commit, mutation, processData) => {
  try {
    const response = await api.post("", { method, params }, { headers });
    if (response.data) {
      let data = response.data.response;
      if (processData) data = processData(data); // 如果需要处理数据，调用处理函数
      console.log(`${method} 返回的数据:`, data);
      commit(mutation, data);
    }
  } catch (error) {
    console.error(`${method} 请求失败:`, error);
  }
};

// Action 定义
const actions = {
  async fetchEquipmentRank({ commit }) {
    await apiRequest(
      "equipment/time_rank",
      { num: 10, start: 1704038400, end: 1735660800 },
      commit,
      "SET_EQUIPMENT_RANK",
      (data) => ({
        yAxisdata: data.map((item) => item.name).reverse(),
        xAxisdata1: data.map((item) => item.time).reverse(),
      })
    );
  },

  async getdata2({ commit }) {
    await apiRequest(
      "equipment/getSummaryInfo",
      {},
      commit,
      "SET_YIQI_STATUS",
      (data) => [
        { name: "正在使用", value: data.usingCount },
        { name: "待机中", value: data.unUsingCount },
        { name: "故障", value: data.outServiceCount },
      ]
    );  
  },

  async getdata3({ commit }) {
    await apiRequest(
      "summarize/userStatus",
      {},
      commit,
      "SET_USER_DISTRIBUTION",
      (data) => ({
        value: [data.outer, data.inner, data.incharge],
        legend: ["校外人员", "校内人员", "管理员"],
      })
    );
  },

  async getdata4({ commit }) {
    await apiRequest(
      "summarize/labStatus",
      {},
      commit,
      "SET_TEST_STATISTICS",
      (data) => [
        { name: "总课题数", value: data.project },
        { name: "课题数", value: data.lab },
        { name: "测试数", value: data.test },
      ]
    );
  },

  async getdata5({ commit }) {
    await apiRequest(
      "eq_reserv/getTopUsers",
      { num: 9, year: 2024 },
      commit,
      "SET_TOP_USERS",
      (data) => ({
        yAxisdata: data.map((item) => item.name).reverse(),
        xAxisdata1: data.map((item) => item.time).reverse(),
      })
    );      
  },
};

// Getters 定义
const getters = {
  equipmentRank: (state) => state.equipmentRank,
  yiqiStatus: (state) => state.yiqiztlist,
  userDistribution: (state) => state.ryfblist,
  testStatistics: (state) => state.ktcslist,
  topUsers: (state) => state.ktzlist,
};

// 导出模块
export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};

