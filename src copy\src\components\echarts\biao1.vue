<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      let label = [
        "01:00",
        "03:00",
        "05:00",
        "07:00",
        "09:00",
        "11:00",
        "12:00",
      ];
      let value = [600, 1000, 1200, 1300, 1600, 1200, 1000, 700];

      const option = {
        grid: {
          top: 20,
          left: 2,
          right: 35,
          bottom: 2,
          containLabel: true,
        },
        legend: {
          data: ["用电量"],
          top: "1%",
          right: "4%",
          textStyle: {
            color: "#ffffff",
            fontSize: 8,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(255,255,255,0)",
                  },
                  {
                    offset: 0.5,
                    color: "rgba(255,255,255,1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(255,255,255,0)",
                  },
                ],
                global: false,
              },
            },
          },
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLabel: {
              formatter: "{value}",
              fontSize: 13,
              margin: 5,
              textStyle: {
                color: "#7ec7ff",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#243753",
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#243753",
              },
            },
            axisTick: {
              show: false,
            },
            data: label,
          },
        ],
        yAxis: [
          {
            boundaryGap: false,
            type: "value",
            axisLabel: {
              fontSize: 13,
              textStyle: {
                color: "#7ec7ff",
              },
            },
            nameTextStyle: {
              color: "#fff",
              fontSize: 13,
              lineHeight: 20,
            },
            splitLine: {
              lineStyle: {
                color: "#243753",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#283352",
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: "用电量",
            type: "line",
            smooth: false, // 设置为 false 以确保是折线而不是曲线
            showSymbol: true,
            symbolSize:10,
            zlevel: 3,
            itemStyle: {
              color: "#19a3df",
              borderColor: "#a3c8d8",
            },
            lineStyle: {
              normal: {
                width: 2,
                color: "#19a3df",
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "rgba(88,255,255,0.2)",
                    },
                    {
                      offset: 0.8,
                      color: "rgba(88,255,255,0)",
                    },
                  ],
                  false
                ),
              },
            },
            label: {
              // 添加数据标签
              show: true,
              position: "top",
              color: "#fff",
              fontSize: 15,
              formatter: "{c}", // 直接显示数值
            },
            data: value,
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 170px;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 170px !important;
  }
}
</style>
