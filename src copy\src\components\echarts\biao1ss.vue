<template>
  <div class="echart" ref="echart"></div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      var option = {
        tooltip: {
          trigger: "item",
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        grid: {
          left: "2%",
          right: "4%",
          bottom: "4%",
          top: "17%",
          containLabel: true,
        },
        legend: {
          data: ["计划工单数", "完成数"],
          right: "0%", // 调整位置
          top: "0%", // 调整位置
          orient: "vertical", // 设置图例为竖直方向显示
          textStyle: {
          
            color: "#fff",
          },
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5, // 调整图例项之间的间距
        },

        xAxis: {
          type: "category",
          data: ["08-01", "08-02", "08-03", "08-04", "08-05"],
          axisLine: {
            lineStyle: {
              color: "#fff",
            },
          },
          axisLabel: {
            textStyle: {
              color: "#fff",
            },
          },
        },

        yAxis: [
          {
            type: "value",
            axisLine: {
              show: false,
              lineStyle: {
                color: "#fff",
              },
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: "#fff",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#fff",
              },
            },
          },
          {
            type: "value",
            name: "",
            nameTextStyle: {
              color: "#666666",
            },
            position: "right",
            axisLine: {
              lineStyle: {
                color: "#cdd5e2",
              },
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              formatter: "{value} %", //右侧Y轴文字显示
              textStyle: {
                color: "#666666",
              },
            },
          },
        ],
        series: [
          {
            name: "计划工单数",
            type: "bar",
            barWidth: "12px",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#F3AE51",
                  },
                  {
                    offset: 1,
                    color: "#F3AE51",
                  },
                ]),
                barBorderRadius: 6,
              },
            },
            data: [400, 400, 300, 300, 300, 400, 400, 400, 300],
          },
          {
            name: "完成数",
            type: "bar",
            barWidth: "12px",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#68E6FD",
                  },
                  {
                    offset: 1,
                    color: "#68E6FD",
                  },
                ]),
                barBorderRadius: 6,
              },
            },
            data: [400, 500, 500, 500, 500, 400, 400, 500, 500],
          },
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.echart {
  width: 100%;
  height: 100%;
}

@media (max-height: 1080px) {
  .echart {
    width: 100%;
    height: 100%;
  }
}
</style>
