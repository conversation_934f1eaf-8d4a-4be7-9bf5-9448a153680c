<template>
  <div class="echart" ref="echart"></div>
</template>
  
<script>
import * as echarts from "echarts";

export default {
  name: "IoTequip",
  data() {
    return {};
  },

  mounted() {
    this.init();
  },

  methods: {
    initData() {},
    init() {
      const myChart = echarts.init(this.$refs.echart);

      app.title = "未优化用电量";

      var data1 = [30, 22, 22, 23, 31, 30, 20];
      var data2 = [1, 2, 1, 2, 1, 1 ,1];
      var data3 = [10, 3, 2, 5, 7, 5, 2];
      var data4 = [9, 30, 9, 60, 70, 20, 59];
   
      var datacity = ["变风量阀门", "通风柜", "变频器", "传感器",];
      const option = {
        color: ["#00FFFF", "#FFFF77", "#FF5511", "#FFD52E"],
        tooltip: {
          trigger: "axis",
        },
        legend: {
          top: "8%",
          right: "40px",
          data: ["正常", "故障", "停用"],
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: "#fff",
            fontSize: 16, 
          },
        },
        grid: {
          //图表的位置
          top: "20%",
          left: "3%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },
        yAxis: [
          {
            axisLine: {
            show: true,
            lineStyle: {
              color: "#F3F4F4",
            },
          },
            type: "value",
            axisLabel: {
              show: true,
              interval: "auto",
              formatter: "{value} ",
              textStyle: {
                color: "#fff",
                fontSize: 16, 
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                type: "dashed",
              },
            },
            show: true,
          },
        ],
        xAxis: [
          
          {
            axisLine: {
            show: true,
            lineStyle: {
              color: "#F3F4F4",
            },
          },
            type: "category",
            axisLabel: {
              interval: 0,
              show: true,
              splitNumber: 15,
              textStyle: {
                fontSize: 16,
                color: "#fff",
              },
            },
            data: datacity,
          },
        ],
        series: [
          {
            name: "正常",
            type: "bar",
            stack: "sum",
            barWidth: "40px",
            data: data1,
          },
          {
            name: "故障",
            type: "bar",
            barWidth: "40px",
            stack: "sum",
            data: data2,
          },
          {
            name: "停用",
            type: "bar",
            color: "#F6931C",
            stack: "sum",
            barWidth: "40px",
            data: data3,
          },
          
        ],
      };

      myChart.setOption(option);
    },
  },
};
</script>
  
<style lang="less" scoped>
.echart {
  width: 98%;
  height: 351px;
}

@media (max-height: 1080px) { 
  .echart {
    width: 98%;
    height: 351px !important;
  }
}
</style>