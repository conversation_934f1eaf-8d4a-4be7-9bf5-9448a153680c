<template>
  <div>
    <component :is="componentTag" :tabledata="tabledata" :zengtiimg="zengtiimg" @fatherMethoddd="fatherMethoddd"
      ref="child"></component>
    <div class="container" v-if="isshow">
      <div class="left-panel" :class="{
        'left-panel-active': showdh,
        'no-animation': noAnimation,
        'left-panel-active1': showdh1,
      }">
        <Title class="ltitle1" tit="平台介绍" :isshow="true">
          <div class="zonghe">
            <div class="boxsty" v-for="item in tablelist" :key="item">
              <div class="mianji">
                <img :src="item.img" class="img" alt="" />
                <div class="wenzi">
                  <div class="top">{{ item.name }}</div>
                  <div class="bottom">{{ item.value }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="gongneng" v-for="item in wenzilist" :key="item">
            <div style="display: flex; align-items: center">
              <div class="yuan"></div>
              <div class="name">{{ item.name }}</div>
            </div>
            <div class="value">{{ item.value }}</div>
          </div>
        </Title>
        <Title class="ltitle1" tit="仪器状态" :isshow="true">
          <div class="boxxx">
            <SystemDete></SystemDete>
          </div>
        </Title>
        <!-- <Title class="ltitle1" tit="能耗统计" :isshow="true">
          <div class="box">
            <div class="zongheqt">
              <div class="left1">
                <div class="mianji" v-for="item in dianlist" :key="item">
                  <img :src="item.img" class="img" alt="" />
                  <div class="wenzis">
                    <div class="top">12346</div>
                    <div class="bottom">
                      <div style="
                          font-family: Alibaba PuHuiTi;
                          font-weight: 400;
                          font-size: 13px;
                          color: #3ba1f4;
                        ">
                        本日
                      </div>
                      /Kwh
                    </div>
                  </div>
                </div>
              </div>
              <biao1></biao1>
            </div>
          </div>
        </Title> -->
      </div>

      <!-- 右侧内容 -->

      <div class="right-panel" :class="{
        'right-panel-active': showdh,
        'no-animation': noAnimation,
        'right-panel-active1': showdh1,
      }">
        <Title1 class="rtitle" tit="今日预约">
          <div class="boxswq">
            <div class="titleimgs">
              <div class="bgu">
                <div>预约总数</div>
                <div>120</div>
              </div>
              <div class="bgu1">
                <div>已完成</div>
                <div>60</div>
              </div>
            </div>
            <div class="titless">
              <div class="item1">仪器名</div>

              <div class="item">预约时长</div>
              <!-- <div class="item">门禁编号</div> -->
              <div class="item">预约状态</div>
            </div>
            <div class="contents" v-for="item in tableDatass" :key="item">
              <div class="item1">{{ item.name }}</div>

              <div class="item">{{ item.duration }}</div>
              <!-- <div class="item">{{ item.roomNumber }}</div> -->
              <div class="item">{{ item.status }}</div>
            </div>
          </div>
        </Title1>
        <Title1 class="rtitle" tit="报警统计">
          <div class="huangxing">
            <huanxing></huanxing>
            <!-- <SystemDete></SystemDete> -->
          </div>
        </Title1>
        <Title1 class="rtitle" tit="异常跟踪处理" :isshow="true">
          <div class="boxxxs">
            <div class="ql-center">
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" style="color: #5c9dee"></div>
                    <div class="pp">未处理</div>
                  </div>
                </div>
                <div class="ql-box1" style="color: #5c9dee">3</div>
              </div>
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status" style="color: #89f6c1"></div>
                    <div class="pp">已完成</div>
                  </div>
                </div>
                <div class="ql-box1 status" style="color: #89f6c1">1</div>
              </div>
              <div class="ql-Box">
                <div class="ql-box">
                  <div class="left_ql">
                    <div class="yuan status"></div>
                    <div class="pp" style="color: #f1c274">处理中</div>
                  </div>
                </div>
                <div class="ql-box1" style="color: #f1c274">2</div>
              </div>
            </div>
            <div class="warning12">
              <div class="info">
                <div class="zongduan">
                  <div class="yuan"></div>
                  <div class="cjhulizhong" style="color: #3ba1f4">处理中</div>
                </div>
                <div class="info1">
                  <p>2024-06-16 12:34</p>
                  <p>3号楼-3F-101</p>
                </div>
                <p class="info2" @click="openbj()">
                  传感器温度报警
                </p>
              </div>
            </div>
            <div class="warning12">
              <div class="info">
                <div class="zongduan">
                  <div class="yuan"></div>
                  <div class="cjhulizhong" style="color: #64f8bb">处理中</div>
                </div>
                <div class="info1">
                  <p>2024-06-16 12:34</p>
                  <p>3号楼-2F-111</p>
                </div>
                <p class="info2" style="color: #64f8bb" @click="openbj()">
                  毒气体泄露报警
                </p>
              </div>
            </div>
            <!-- <div class="warning12">
              <div class="info">
                <div class="zongduan">
                  <div class="yuan"></div>
                  <div class="cjhulizhong" style="color: #fabf69">处理中</div>
                </div>
                <div class="info1">
                  <p>2024-06-16 12:34</p>
                  <p>3号楼-4F-102</p>
                </div>
                <p class="info2" style="color: #fabf69" @click="openbj()">
                  烟雾传感器报警
                </p>
              </div>
            </div> -->
          </div>
        </Title1>
      </div>
    </div>

    <table-2 class="table2" @close="closetan" v-if="opentable2"></table-2>
    <!-- <div
      class="center_container"
      :class="{
        'right-panel-active11': showdh,
        'no-animation': noAnimation,
        'right-panel-active12': showdh1,
      }"
    >
      <img
        class="btn"
        src="../assets/image/shang.png"
        @click="scrollUp"
        alt="向上"
      />
      <div class="content" ref="content">
        <div
          :class="activef == index ? 'itema' : 'item'"
          v-for="(item, index) in resItems"
          :key="index"
          @click="switchactivef(item, index)"
          @mouseover="hoveredRoom = item"
          @mouseleave="hoveredRoom = null"
        >
          {{
            index === 0
              ? title + "F-" + "整体"
              : title + "F-" + (index < 10 ? "10" + index : "1" + index)
          }}

          <div class="tooltip" v-if="hoveredRoom === item">{{ item.name }}</div>
        </div>
      </div>
      <img
        class="btn"
        src="../assets/image/xia.png"
        @click="scrollDown"
        alt="向下"
      />
    </div>
    <div
      @click="returnhome()"
      class="return"
      :class="{
        'right-panel-active11': showdh,
        'no-animation': noAnimation,
        'right-panel-active12': showdh1,
      }"
    >
      返回
    </div> -->
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import zhexian1 from "@/components/echarts/zhexian1.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
import echarts1 from "@/components/echarts/bingjifang/echarts3.vue";
import echarts2 from "@/components/echarts/bingjifang/echarts5.vue";
import table2 from "@/components/common/table2.vue";
import shuangxiang from "@/components/echarts/shuangxiang.vue";
import shebei from "@/views/shebei.vue";
import { resourceDeviceList } from "@/api/admin.js";
import biao1 from "../components/echarts/biao1.vue";
import biao1ss from "../components/echarts/biao1ss.vue";

// resourceDeviceList
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    table2,
    huanxing,
    zhexian,
    zhexian1,
    SystemDete,
    echarts1,
    echarts2,
    shuangxiang,
    shebei,
    biao1,
    biao1ss,
  },
  props: ["title", "resItems"],
  data() {
    // 这里存放数据
    return {
      tableDatass: [
        {
          name: "中孔分析仪",
          date: "2024-09-01",
          duration: "1:00-3:00",
          roomNumber: "001",
          status: "已成功",
        },
        {
          name: "X射线光电子能谱仪",
          date: "2024-09-01",
          duration: "1:00-3:00",
          roomNumber: "001",
          status: "已成功",
        },
        {
          name: "总有机碳分析仪",
          date: "2024-09-01",
          duration: "1:00-3:00",
          roomNumber: "001",
          status: "已成功",
        },
        {
          name: "AKTA蛋白纯化仪",
          date: "2024-09-01",
          duration: "1:00-3:00",
          roomNumber: "001",
          status: "未成功",
        },
        {
          name: "电学测试仪",
          date: "2024-09-01",
          duration: "1:00-3:00",
          roomNumber: "001",
          status: "未成功",
        },
      ],
      opentable2: false,
      hoveredRoom: null,
      scrollPosition: 0,
      flag: true,
      localtitle: "",
      dianlist: [
        {
          name: "总用地面积",
          value: "57874.1㎡",
          img: require("../assets/image/ri.png"),
        },
        {
          name: "总建筑面积",
          value: "7802.54㎡",
          img: require("../assets/image/zhou.png"),
        },
        {
          name: "地上建筑面积",
          value: "60722.09㎡",
          img: require("../assets/image/yue.png"),
        },
      ],
      tablelist: [
        {
          name: "总用地面积",
          value: "57874.1㎡",
          img: require("../assets/image/mianji1.png"),
        },
        {
          name: "总建筑面积",
          value: "7802.54㎡",
          img: require("../assets/image/mianji2.png"),
        },
        {
          name: "地上建筑面积",
          value: "60722.09㎡",
          img: require("../assets/image/mianji3.png"),
        },
        {
          name: "地下建筑面积",
          value: "17080.45㎡",
          img: require("../assets/image/mianji4.png"),
        },
      ],
      wenzilist: [
        {
          name: "平台概述",
          value:
            "天津大学大型仪器管理平台是天津大学校级大型仪器管理平台，平台仪器由天津大学分析测试中心、化工学院、材料学院、环境学院、理学院、药学院、精仪学院、机械学院、建工学院、信息学院、自动化学院和建筑学院等单位的大型仪器设备组成。 平台施行统一管理、资源共享、有偿服务。",
        },
      ],

      activef: 0,
      isshow: true,
      isactive: 0,
      tabledata: [],
      zengtiimg: "",
      lrdata: [
        {
          title1: "温度",
          title2: "22℃",
          title3: "2022-04-01 12:00:00",
        },
      ],
      deviceTypes: "CQQ11",
      activeTab: "today",
      botlist: [
        { name: "总览", code: "" },
        { name: "设备列表", code: "" },
        {
          name: "环境温湿度",
          code: "CGQ11",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/wensiduchuanganqi.png",
        },
        {
          name: "防爆温湿度",
          code: "CGQ10",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png",
        },
        {
          name: "冰箱状态",
          code: "LRY193",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png",
        },
        {
          name: "培养箱状态",
          code: "CGQ13",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/bingxiang.png",
        },
        {
          name: "乙炔气体",
          code: "CGQ7",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/fangbao.png",
        },
        {
          name: "环境CO2",
          code: "CGQ9",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png",
        },
        {
          name: "环境O2",
          code: "CGQ3",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/huanjingco2.png",
        },
        {
          name: "甲烷气体",
          code: "CGQ8",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/jiawan.png",
        },
        {
          name: "房间压差",
          code: "CGQ2",
          img: "http://3d.dddtask.cn/engineer-qilei/fengxian/kqyc.png",
        },
      ],
      listst: [
        {
          name: "广东质检中诚认证有限公司到中广...",
        },
        { name: "材料科学、化学工程及医药研发成..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
        { name: "植酸检测方法及作用" },
        { name: "兔拉检测：行业中芯片造假技术..." },
        { name: "酒精代谢能力检测：您的酒量，基..." },
      ],
      showdh: true,
      showdh1: false,
      noAnimation: false,
      localTitle: this.title, // 初始化本地数据属性
      nhlist: [
        {
          title: "供气压力",
          status: "0.3Mpa",
          unit: "℃",
        },

        {
          title: "供气流量",
          status: "6M3/min",
          unit: "㎡",
        },
        {
          title: "露点温度",
          status: "6℃",
          unit: "℃",
        },
        {
          title: "含氧量",
          status: "6PPM",
          unit: "㎡",
        },
      ],
      warnlist1: [
        {
          type: 1,
          time: "视频监控报警-3号楼-3F-101",
          value: "",
          name: "2024-06-16   12:34:09",
        },
        {
          type: 2,
          time: "视频监控报警-3号楼-3F-101",
          value: "",
          name: "2024-06-16   12:34:09",
        },
        {
          type: 3,
          name: "2024-06-16   12:34:09",
          value: "",
          time: "视频监控报警-3号楼-3F-101",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "",
      dectid: "",
    };
  },
  // 计算属性类似于data概念
  computed: {
    formattedTitle() {
      return {
        title: `${this.localTitle}F实验室介绍`,
        img: require(`../assets/img/floor/1Fbig.png`),
      };
    },
    formattedTitle1() {
      return `${this.localTitle}F实验室总览`;
    },
    formattedTitle2() {
      return `实验室${this.localTitle}F环境信息`;
    },
    formattedTitle3() {
      return `实验室${this.localTitle}F设备信息`;
    },
    formattedTitle4() {
      return `实验室${this.localTitle}F事件详情`;
    },
    formatted1Title() {
      return {
        title: `${this.localTitle}实验室介绍`,
        img: require(`../assets/img/floor/${this.title}Fbig.png`),
      };
    },
    formatted1Title1() {
      return `${this.localTitle}实验室总览`;
    },
    formatted1Title2() {
      return `${this.localTitle}环境信息`;
    },
    formatted1Title3() {
      return `${this.localTitle}设备信息`;
    },
    formatted1Title4() {
      return `${this.localTitle}事件详情`;
    },
  },
  // 监控data中的数据变化
  watch: {
    title(newVal) {
      this.localTitle = newVal;
    },
    resItems(newVal) {
      console.log(newVal);

      // this.resItems = newVal;
    },
  },
  // 方法集合
  methods: {
    closetan() {
      this.opentable2 = false;
    },
    openbj() {
      this.opentable2 = true;
    },
    handleOpenDialog() {
      console.log(1111);
      this.$emit("open-bj");
    },
    scrollUp() {
      const content = this.$refs.content;
      content.scrollTop -= 38; // 每次向上滑动25px
    },
    scrollDown() {
      const content = this.$refs.content;
      content.scrollTop += 38; // 每次向下滑动25px
    },
    returnhome() {
      this.$emit("returnhome");
    },
    async switchactivef(item, index) {
      this.dectid = item.id;
      const res = await resourceDeviceList({
        resourceId: item.id,
        deviceTypes: this.deviceTypes,
      });

      this.tabledata = res.data;

      if (index) {
        this.flag = false;
        this.localTitle = item.roomid;
      } else {
        this.localTitle = this.title;
        this.flag = true;
      }
      console.log(item);
      this.activef = index;
      // this.$emit("childEvent", title, index);
    },
    slideUp() {
      const contentHeight = this.$refs.content.scrollHeight;
      if (this.position > -contentHeight + this.containerHeight) {
        this.position -= this.step;
        this.$refs.content.style.transform = `translateY(${this.position}px)`;
      }
    },
    slideDown() {
      if (this.position < 0) {
        this.position += this.step;
        this.$refs.content.style.transform = `translateY(${this.position}px)`;
      }
    },

    //  this.dectid = item.id;
    //     const res = await resourceDeviceList({
    //       resourceId: item.id,
    //       deviceTypes: this.deviceTypes,
    //     });
    //     console.log(res.data, "qilei");
    //     this.tabledata = res.data;

    async switchTab1(item, index) {
      console.log(item.img);
      this.zengtiimg = item.img;

      this.deviceTypes = item.code;
      const res = await resourceDeviceList({
        resourceId: this.dectid,
        deviceTypes: this.deviceTypes,
      });

      this.tabledata = res.data;

      // this.switchactivef(item, item.code);
      this.isactive = index;
      if (index) {
        this.componentTag = "shebei";
        this.isshow = false;
        this.showdh = true;
        this.showdh1 = false;
      } else {
        this.componentTag = "";
        this.isshow = true;
        this.showdh = false;
        this.showdh1 = true;
      }
    },
    switchTab(tab) {
      this.activeTab = tab;
    },
    qeihuan(index) {
      console.log(index, "123123");
    },

    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    getClassForStatus(status) {
      if (status === "告警总数") {
        return "completed";
      } else if (status === "处理完") {
        return "incomplete";
      } else if (status === "未处理") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "告警总数") {
        return "completeds";
      } else if (status === "处理完") {
        return "incompletes";
      } else if (status === "未处理") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    qiehuanyans(index) {
      console.log(index, "123123");
      this.currentIndex = index;
    },
    oc(value) {
      console.log(value, "floor收到的值");
      this.showdh = value;
      if (!this.componentTag == "") {
        this.$refs.child.oc(value);
      }
    },
    getClassForStatus(status) {
      if (status === "巡检中") {
        return "completed";
      } else if (status === "待巡检") {
        return "incomplete";
      } else if (status === "已完成") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "巡检中") {
        return "completeds";
      } else if (status === "待巡检") {
        return "incompletes";
      } else if (status === "已完成") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.showdh1 = true;
    setTimeout(() => {
      this.showdh1 = false;
      this.noAnimation = false;
    }, 1000); // 动画持续时间为1秒
    console.log(1222);
    ue.interface.setSliderValue = (value) => {
      console.log(value, "ue点击拿到的值");
      if (!isNaN(Number(value.data))) {
        // let did = value.data; // 如果是数字，则赋值
        // const result = this.sblist.filter(item => item.id == did);
        // this.deviceId = result[0].deviceId
        // console.log(this.deviceId, 'ue点击拿到的id');
      }
      // this.deid = JSON.parse(value.data) - 43846
      // console.log(this.deid);
      // if (!isNaN(parseInt(value.data, 10))) {
      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))
      //   console.log(dtdata1);
      //   this.showdet = false
      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;
      //   // console.log(this.did);
      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);
      //   let data1 = dtdata1.find(item => item.id == value.data)
      //   // this.details = didata
      //   this.bid = data1.bid
      //   this.fid = data1.fid
      //   // this.hlsurl
      //   // this.bm = data1.note
      //   console.log(data1, 1111111);
      //   // this.getCameraData(did)
      // }
    };
  },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  }, // 生命周期 - 销毁之前
  destroyed() {
    console.log(1221);
  }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.table2 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 99999;
}

.return {
  position: fixed;
  right: 373px;
  top: 100px;
  height: 44px;
  width: 46px;
  // overflow: hidden;
  transform: translate(720%);
  transition: transform 0.5s ease-in-out;

  z-index: 999;
  cursor: pointer;
  text-align: center;
  line-height: 67px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 11px;
  color: #ffffff;
  background: url("../assets/image/return.png");
  background-size: 100% 100%;
}

.center_container {
  position: fixed;
  right: 359px;
  top: 352px;
  height: 401px;
  width: 70px;
  // overflow: hidden;
  transform: translate(470%);
  transition: transform 0.5s ease-in-out;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: url("../assets/image/louceng.png");
  background-size: 100% 100%;

  .content::-webkit-scrollbar {
    width: 0px;
    display: none;
    /* 设置滚动条的宽度 */
  }

  /* 设置滚动条轨道的样式 */
  .content::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条轨道的背景色 */
  }

  /* 设置滚动条滑块的样式 */
  .content::-webkit-scrollbar-thumb {
    background-color: #888;
    /* 设置滚动条滑块的背景色 */
  }

  /* 鼠标悬停在滚动条上时的样式 */
  .content::-webkit-scrollbar-thumb:hover {
    background-color: #555;
    /* 设置鼠标悬停时滚动条滑块的背景色 */
  }

  .content {
    height: 330px;
    /* 内容区的总高度，视实际内容而定 */
    transition: transform 0.5s ease;
    overflow-y: auto;
    text-align: center;

    /* 设置滚动条的样式 */

    .item {
      cursor: pointer;
      width: 75px;
      height: 25px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 14px;
      color: #86a6b7;
      line-height: 25px;
      margin-top: 12px;
    }

    .itema {
      background: url("../assets/image/lcactive.png");
      background-size: 100% 100%;
      cursor: pointer;
      width: 66px;
      height: 25px;
      font-family: YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      line-height: 25px;
      margin-top: 12px;
    }

    .tooltip {
      position: absolute;
      left: 80%;
      // top: 15px;
      background-color: #1a3867;
      border: 1px solid #7ba6eb;
      color: #fff;
      padding: 5px;
      z-index: 1;
      white-space: nowrap;
      font-size: 12px;
      visibility: hidden;

      opacity: 0;
      transition: opacity 0.5s, visibility 0.5s;
      z-index: 999;
      font-family: Source Han Sans SC;
    }

    .item:hover .tooltip {
      visibility: visible;
      /* 当鼠标悬停时显示 */
      opacity: 1;
    }

    .itema:hover .tooltip {
      visibility: visible;
      /* 当鼠标悬停时显示 */
      opacity: 1;
    }
  }
}

.btn {
  margin-top: 13px;
  width: 27px;
  height: 14px;
  cursor: pointer;
}

.echart2 {
  height: 180px;
}

.bott {
  position: fixed;
  z-index: 1;
  bottom: 4px;
  // left: 6px;
  width: 1920px;
  height: 50px;
  display: flex;
  flex-direction: row;
  cursor: pointer;
  text-align: center;

  .bottit {
    width: 153px;
    height: 45px;
    background: url("../assets/image/bot_b.png");
    background-size: 100% 100%;
    margin-left: 19.496px;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 17px;
    color: #ffffff;
    line-height: 43px;
    cursor: pointer;
  }

  .bottit1 {
    width: 153px;
    height: 69px;
    background: url("../assets/image/bot_a.png");
    background-size: 100% 100%;
    margin-left: 19.496px;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 17px;
    color: #ffffff;
    line-height: 87px;
    cursor: pointer;
    margin-top: -23px;
  }
}

.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .left-panel {
    position: fixed;
    z-index: 1;
    top: 75px;
    left: 22px;
    width: 387px;
    height: 937px;
    background-size: 100% 100%;
    transform: translate(-122%);
    transition: transform 0.5s ease-in-out;

    .box {
      // margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      // width: 330px;
      // height: 404px;

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
        width: 296px;
        height: 178px;
      }

      .wenzi {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 10px;
        color: #bdecf9;
        text-align: left;
        margin-left: 20px;
        margin-right: 20px;
      }

      .p {
        text-indent: 2em;
        margin-bottom: 1em;
        letter-spacing: 0.05em;
      }
    }
  }

  .left-panel-active {
    transform: translate(0%);
  }

  .left-panel-active1 {
    // transform: translate(0%);
    animation: slideOut 1s ease-in-out forwards;
  }

  @keyframes slideOut {
    100% {
      transform: translateX(0%);
    }

    // 85% {
    //   transform: translateX(-25%);
    // }

    // 65% {
    //   transform: translateX(-15%);
    // }

    // 40% {
    //   transform: translateX(-55%);
    // }

    // 30% {
    //   transform: translateX(-40%);
    // }

    0% {
      transform: translateX(-100%);
    }
  }

  .rtitle {
    margin-top: 16px;
  }

  .ltitle1 {
    margin-top: 16px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 22px;
    width: 387px;
    top: 75px;
    height: 937px;

    background-size: 100% 100%;
    transform: translate(122%);
    transition: transform 0.5s ease-in-out;

    .box {
      // margin-top: 6px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      font-family: Source Han Sans SC;
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      width: 330px;
      height: 224px;

      .titlest {
        display: flex;

        // shiyansimg.png
        .itm {
          cursor: pointer;
          margin: 16px 9px 0 10px;
          background: url("../assets/image/shiyansimg.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 100px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .itms {
          background: url("../assets/image/xuanzexuanzhong.png") !important;
          background-size: 100% 100%;
          background-repeat: no-repeat;
          width: 100px;
          height: 41px !important;
          padding-bottom: 10px;
        }
      }

      .contentss {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-around;
        align-items: center;

        .itm {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 112px;
          height: 70px;
          background: url("../assets/image/wendupng.png");
          background-size: 100% 100%;
          background-repeat: no-repeat;
          font-family: DIN;
          font-weight: bold;
          font-size: 22px;
          color: #ffffff;

          .danwei {
            font-family: DIN;
            font-weight: bold;
            font-size: 12px;
            color: #ffffff;
          }
        }

        .wendyu {
          font-family: Source Han Sans SC;
          font-weight: 400;
          font-size: 13px;
          color: #ffffff;
          margin-top: -7px;
        }
      }

      .loudong {
        margin-top: 21px;
        margin-bottom: 25px;
        width: 296px;
        height: 178px;
      }
    }

    .boxxxs {
      margin-left: 8px;
      margin-top: 1px;
      margin-bottom: 18px;
      // background: url("../assets/image/zuoshang1.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;

      width: 330px;
      // height: 254px;
    }
  }

  .boxxx {
    // margin-top: 6px;
    margin-bottom: 18px;
    // background: url("../assets/image/zuoshang1.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    width: 350px;
    height: 234px;
  }

  .no-animation {
    transition: none;
  }

  .right-panel-active {
    transform: translate(0%);
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active1 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards;
  }

  .right-panel-active11 {
    transform: translate(0%) !important;
    // animation: slideIn 1s ease-in-out ;
  }

  .right-panel-active12 {
    // transform: translate(0%);
    animation: slideIn 1s ease-in-out forwards !important;
  }

  @keyframes slideIn {
    0% {
      transform: translateX(100%);
    }

    // 30% {
    //   transform: translateX(65%);
    // }

    // 40% {
    //   transform: translateX(40%);
    // }

    // 65% {
    //   transform: translateX(15%);
    // }

    // 85% {
    //   transform: translateX(25%);
    // }

    100% {
      transform: translateX(0%);
    }
  }

  .completed {
    background: #7ad0ff;
  }

  .incomplete {
    background: #ff6041;
  }

  .warning {
    background: #00ffc0;
  }

  .completeds {
    color: #7ad0ff;
  }

  .incompletes {
    color: #ff6041;
  }

  .warnings {
    color: #00ffc0;
  }
}

.ql-center {
  display: flex;
  // margin-top: 20px;
  justify-content: space-around;
  margin-top: 6px;
  // padding-top: 10px;

  .ql-Box {
    width: 30%;
    height: 49px;
    border: 1px solid #7ad0ff;
    // opacity: 0.6;
    border-radius: 2px;

    .ql-box1 {
      font-family: Alibaba PuHuiTi;
      font-weight: bold;
      font-size: 19px;
      color: #7ad0ff;
    }

    .ql-box {
      display: flex;
      padding-left: 23px;
      padding-right: 9px;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 24px;

      .left_ql {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #ffffff;

        .yuan {
          width: 8px;
          height:8px;
          border-radius: 50%;
        }

        .pp {
          margin-left: 5px;
          color: #fff;
          font-size: 16px;
        }
      }

      img {
        height: 12px;
        width: 7px;
      }
    }
  }
}

.warn1 {
  // background: url("../assets/image/warnred.png");
}

.warn2 {
  // background: url("../assets/image/warnyellow.png");
}

.warn3 {
  // background: url("../assets/image/warngreen.png");
}

.warning12 {
  background-size: 100% 100%;
  // width: 365px;
  height: 47px;

  .info {
    margin-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    text-align: left;
    font-size: 13px;
    margin-top: 10px;
    .info1 {
      display: flex;
      flex-direction: column;

      p:nth-of-type(1) {
    
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
      }

      .red {
        color: #ff0000;
      }

      .green {
        color: #00ffcc;
      }

      .yellow {
        color: #ffff00;
      }

      p:nth-of-type(2) {
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-size: 16px;
        color: #ffffff;
      }
    }

    .info2 {
      cursor: pointer;
      margin-right: 10px;
      font-size: 15px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #cffff8;
    }
  }
}

.zonghe {
  // margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  .boxsty {
    width: 50%;
    margin-top: 12px;

    .mianji {
      display: flex;
      align-items: center;

      .img {
      width: 50px;
      height: 49px;
    }

      .wenzi {
        text-align: left;
        margin-left: 5px;

        .top {
          // margin-bottom: 9px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 20px;
          color: #ffffff;
        }

        .bottom {
          font-family: Source Han Sans SC;
          font-weight: 500;
          font-size: 20px;
          color: #59ffc4;
        }
      }
    }
  }
}

.gongneng {
  margin-top: 12px;

  display: flex;
  flex-direction: column;
  // align-items: center;
  font-family: ZiTiGuanJiaFangSongTi;
  font-weight: bold;
  font-size:22px;
  color: #59ffc4;
  text-align: left;

  .yuan {
    margin-right: 7px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #85fdca;
  }

  .value {
    font-family: ZiTiGuanJiaFangSongTi;
    font-weight:550;
    font-size: 22px;
    color: #fff;
    width:100%;
    margin-right: 3px;
    text-indent:42px
  }

  .name {
    // width: 58px;
    font-size: 22px;
  }
}

.zongheqt {
  .left1 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-right: 20px;
    margin-top: 7px;

    .mianji {
      background: url("../assets/image/zengfangti.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 106px;
      height: 58px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .img {
      width: 50px;
      height: 49px;
    }

    .wenzis {
      .top {
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
      }

      .bottom {
        display: flex;
        align-items: flex-end;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        font-size: 13px;
        color: #fff;
        margin-left: 7px;
      }
    }
  }
}

.boxswq {
  width: 365px;
  height: 242px;
}

.huangxing {
  width: 359px;
  height: 238px;
}

.cjhulizhong {
  font-family: Microsoft YaHei;
  font-weight: bold;
  font-size: 14px;
  color: #64f8bb;
  margin-left: 8px;
}

.yuan {
  width: 10px;
  height: 10px;
  background-color: #518acd;
  border-radius: 50%;
}

.zongduan {
  display: flex;
  align-items: center;
  font-size:13px;
}

.titleimgs {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-right: 10px;

  .bgu {
    background-color: #95871cbf !important;

    // background: url("../assets/image/titlessimg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 171px;
    height: 38px;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 22px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px 0 14px;
  }

  .bgu1 {
    background-color: rgb(28, 128, 149) !important;

    // background: url("../assets/image/titlessimg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 171px;
    height: 38px;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 22px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px 0 14px;
  }
}

.titless {
  margin-right: 10px;
  width: 96%;
  background: rgba(25, 37, 60, 0.5);
  height: 32px;
  margin-top: 8px;
  display: flex;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 19px;
  color: #40d7ff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .item {
    width: 100%;
    flex:1;
  }
  .item1 {
    width: 100%;
    flex:1.8;
  }
}

.contents {
  border-bottom: 1px solid #3b5471;
  margin-right: 10px;
  width: 96%;
  background: rgba(45, 58, 79, 0.2);
  height: 32px;

  display: flex;
  font-family: Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 18px;
  color: #fff;
  display: flex;
  align-items: center;

  .item {
    width: 100%;
    flex:1;
  }
  .item1 {
    width: 100%;
    flex:1.8;
  }
}

.contents:nth-child(odd) {
  background: rgba(46, 61, 83, 0.4);
  /* 深色背景 */
}

.contents:nth-child(even) {
  background: rgba(37, 50, 69, 0.2);
  /* 浅色背景 */
}
</style>
